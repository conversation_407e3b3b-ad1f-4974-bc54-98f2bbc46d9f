import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  HazardType,
  HazardDescription,
} from '../models';
import {HazardTypeRepository} from '../repositories';

export class HazardTypeHazardDescriptionController {
  constructor(
    @repository(HazardTypeRepository) protected hazardTypeRepository: HazardTypeRepository,
  ) { }

  @get('/hazard-types/{id}/hazard-descriptions', {
    responses: {
      '200': {
        description: 'Array of HazardType has many HazardDescription',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(HazardDescription)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<HazardDescription>,
  ): Promise<HazardDescription[]> {
    return this.hazardTypeRepository.hazardDescriptions(id).find(filter);
  }

  @post('/hazard-types/{id}/hazard-descriptions', {
    responses: {
      '200': {
        description: 'HazardType model instance',
        content: {'application/json': {schema: getModelSchemaRef(HazardDescription)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof HazardType.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardDescription, {
            title: 'NewHazardDescriptionInHazardType',
            exclude: ['id'],
            optional: ['hazardTypeId']
          }),
        },
      },
    }) hazardDescription: Omit<HazardDescription, 'id'>,
  ): Promise<HazardDescription> {
    return this.hazardTypeRepository.hazardDescriptions(id).create(hazardDescription);
  }

  @patch('/hazard-types/{id}/hazard-descriptions', {
    responses: {
      '200': {
        description: 'HazardType.HazardDescription PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardDescription, {partial: true}),
        },
      },
    })
    hazardDescription: Partial<HazardDescription>,
    @param.query.object('where', getWhereSchemaFor(HazardDescription)) where?: Where<HazardDescription>,
  ): Promise<Count> {
    return this.hazardTypeRepository.hazardDescriptions(id).patch(hazardDescription, where);
  }

  @del('/hazard-types/{id}/hazard-descriptions', {
    responses: {
      '200': {
        description: 'HazardType.HazardDescription DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(HazardDescription)) where?: Where<HazardDescription>,
  ): Promise<Count> {
    return this.hazardTypeRepository.hazardDescriptions(id).delete(where);
  }
}
