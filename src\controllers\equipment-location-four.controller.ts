import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Equipment,
  LocationFour,
} from '../models';
import {EquipmentRepository} from '../repositories';

export class EquipmentLocationFourController {
  constructor(
    @repository(EquipmentRepository)
    public equipmentRepository: EquipmentRepository,
  ) { }

  @get('/equipment/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to Equipment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationFour)},
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof Equipment.prototype.id,
  ): Promise<LocationFour> {
    return this.equipmentRepository.locationFour(id);
  }
}
