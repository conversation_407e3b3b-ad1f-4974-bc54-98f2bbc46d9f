import { Entity, model, property, belongsTo } from '@loopback/repository';
import { User } from './user.model';
import { Checklist } from './checklist.model';
import { LocationOne } from './location-one.model';
import { LocationTwo } from './location-two.model';
import { LocationThree } from './location-three.model';
import { LocationFour } from './location-four.model';

@model()
export class Audit extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  month?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  dateTime?: string;

  @property({
    type: 'string',
  })
  endDateTime?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  year?: string;

  @property({
    type: 'any',
  })
  checklistReport?: any;

  @property({
    type: 'any',
  })
  notesReport?: any;

  @property({
    type: 'any',
  })
  headerInformation?: any;

  @property({
    type: 'array',
    itemType: 'object',
  })
  actions?: object[];

  @property({
    type: 'any',

  })
  auditData?: any;

  @property({
    type: 'string',
  })
  remarks?: string;

  @belongsTo(() => User)
  assignedToId: string;

  @belongsTo(() => Checklist)
  checklistId: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  constructor(data?: Partial<Audit>) {
    super(data);
  }
}

export interface AuditRelations {
  // describe navigational properties here
}

export type AuditWithRelations = Audit & AuditRelations;
