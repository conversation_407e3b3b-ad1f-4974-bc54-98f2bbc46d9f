import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  HazardType,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportHazardTypeController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/hazard-type', {
    responses: {
      '200': {
        description: 'HazardType belonging to ObservationReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(HazardType),
          },
        },
      },
    },
  })
  async getHazardType(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<HazardType> {
    return this.observationReportRepository.hazardType(id);
  }
}
