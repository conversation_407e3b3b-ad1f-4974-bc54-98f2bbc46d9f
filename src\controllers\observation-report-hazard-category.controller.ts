import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  HazardCategory,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportHazardCategoryController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/hazard-category', {
    responses: {
      '200': {
        description: 'HazardCategory belonging to ObservationReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(HazardCategory),
          },
        },
      },
    },
  })
  async getHazardCategory(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<HazardCategory> {
    return this.observationReportRepository.hazardCategory(id);
  }
}
