import {Entity, model, property} from '@loopback/repository';

@model()
export class PlantRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  permission?: any;

  @property({
    type: 'date',
  })
  created?: string;


  constructor(data?: Partial<PlantRole>) {
    super(data);
  }
}

export interface PlantRoleRelations {
  // describe navigational properties here
}

export type PlantRoleWithRelations = PlantRole & PlantRoleRelations;
