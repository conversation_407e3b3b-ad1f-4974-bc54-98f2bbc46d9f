import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {EptwRole} from '../models';
import {EptwRoleRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class EptwRoleController {
  constructor(
    @repository(EptwRoleRepository)
    public eptwRoleRepository : EptwRoleRepository,
  ) {}

  @post('/eptw-roles')
  @response(200, {
    description: 'EptwRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(EptwRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EptwRole, {
            title: 'NewEptwRole',
            exclude: ['id'],
          }),
        },
      },
    })
    eptwRole: Omit<EptwRole, 'id'>,
  ): Promise<EptwRole> {
    return this.eptwRoleRepository.create(eptwRole);
  }

  @get('/eptw-roles/count')
  @response(200, {
    description: 'EptwRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(EptwRole) where?: Where<EptwRole>,
  ): Promise<Count> {
    return this.eptwRoleRepository.count(where);
  }

  @get('/eptw-roles')
  @response(200, {
    description: 'Array of EptwRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EptwRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(EptwRole) filter?: Filter<EptwRole>,
  ): Promise<EptwRole[]> {
    return this.eptwRoleRepository.find(filter);
  }

  @patch('/eptw-roles')
  @response(200, {
    description: 'EptwRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EptwRole, {partial: true}),
        },
      },
    })
    eptwRole: EptwRole,
    @param.where(EptwRole) where?: Where<EptwRole>,
  ): Promise<Count> {
    return this.eptwRoleRepository.updateAll(eptwRole, where);
  }

  @get('/eptw-roles/{id}')
  @response(200, {
    description: 'EptwRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(EptwRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(EptwRole, {exclude: 'where'}) filter?: FilterExcludingWhere<EptwRole>
  ): Promise<EptwRole> {
    return this.eptwRoleRepository.findById(id, filter);
  }

  @patch('/eptw-roles/{id}')
  @response(204, {
    description: 'EptwRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EptwRole, {partial: true}),
        },
      },
    })
    eptwRole: EptwRole,
  ): Promise<void> {
    await this.eptwRoleRepository.updateById(id, eptwRole);
  }

  @put('/eptw-roles/{id}')
  @response(204, {
    description: 'EptwRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() eptwRole: EptwRole,
  ): Promise<void> {
    await this.eptwRoleRepository.replaceById(id, eptwRole);
  }

  @del('/eptw-roles/{id}')
  @response(204, {
    description: 'EptwRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.eptwRoleRepository.deleteById(id);
  }
}
