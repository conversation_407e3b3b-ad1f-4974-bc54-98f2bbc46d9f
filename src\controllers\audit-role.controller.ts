import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AuditRole} from '../models';
import {AuditRoleRepository} from '../repositories';

export class AuditRoleController {
  constructor(
    @repository(AuditRoleRepository)
    public auditRoleRepository : AuditRoleRepository,
  ) {}

  @post('/audit-roles')
  @response(200, {
    description: 'AuditRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(AuditRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditRole, {
            title: 'NewAuditRole',
            exclude: ['id'],
          }),
        },
      },
    })
    auditRole: Omit<AuditRole, 'id'>,
  ): Promise<AuditRole> {
    return this.auditRoleRepository.create(auditRole);
  }

  @get('/audit-roles/count')
  @response(200, {
    description: 'AuditRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AuditRole) where?: Where<AuditRole>,
  ): Promise<Count> {
    return this.auditRoleRepository.count(where);
  }

  @get('/audit-roles')
  @response(200, {
    description: 'Array of AuditRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AuditRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AuditRole) filter?: Filter<AuditRole>,
  ): Promise<AuditRole[]> {
    return this.auditRoleRepository.find(filter);
  }

  @patch('/audit-roles')
  @response(200, {
    description: 'AuditRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditRole, {partial: true}),
        },
      },
    })
    auditRole: AuditRole,
    @param.where(AuditRole) where?: Where<AuditRole>,
  ): Promise<Count> {
    return this.auditRoleRepository.updateAll(auditRole, where);
  }

  @get('/audit-roles/{id}')
  @response(200, {
    description: 'AuditRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AuditRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AuditRole, {exclude: 'where'}) filter?: FilterExcludingWhere<AuditRole>
  ): Promise<AuditRole> {
    return this.auditRoleRepository.findById(id, filter);
  }

  @patch('/audit-roles/{id}')
  @response(204, {
    description: 'AuditRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditRole, {partial: true}),
        },
      },
    })
    auditRole: AuditRole,
  ): Promise<void> {
    await this.auditRoleRepository.updateById(id, auditRole);
  }

  @put('/audit-roles/{id}')
  @response(204, {
    description: 'AuditRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() auditRole: AuditRole,
  ): Promise<void> {
    await this.auditRoleRepository.replaceById(id, auditRole);
  }

  @del('/audit-roles/{id}')
  @response(204, {
    description: 'AuditRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.auditRoleRepository.deleteById(id);
  }
}
