import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AuditGmsThree} from '../models';
import {AuditGmsThreeRepository} from '../repositories';

export class AuditGmsThreeController {
  constructor(
    @repository(AuditGmsThreeRepository)
    public auditGmsThreeRepository : AuditGmsThreeRepository,
  ) {}

  @post('/audit-gms-threes')
  @response(200, {
    description: 'AuditGmsThree model instance',
    content: {'application/json': {schema: getModelSchemaRef(AuditGmsThree)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsThree, {
            title: 'NewAuditGmsThree',
            exclude: ['id'],
          }),
        },
      },
    })
    auditGmsThree: Omit<AuditGmsThree, 'id'>,
  ): Promise<AuditGmsThree> {
    return this.auditGmsThreeRepository.create(auditGmsThree);
  }

  @get('/audit-gms-threes/count')
  @response(200, {
    description: 'AuditGmsThree model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AuditGmsThree) where?: Where<AuditGmsThree>,
  ): Promise<Count> {
    return this.auditGmsThreeRepository.count(where);
  }

  @get('/audit-gms-threes')
  @response(200, {
    description: 'Array of AuditGmsThree model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AuditGmsThree, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AuditGmsThree) filter?: Filter<AuditGmsThree>,
  ): Promise<AuditGmsThree[]> {
    return this.auditGmsThreeRepository.find(filter);
  }

  @patch('/audit-gms-threes')
  @response(200, {
    description: 'AuditGmsThree PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsThree, {partial: true}),
        },
      },
    })
    auditGmsThree: AuditGmsThree,
    @param.where(AuditGmsThree) where?: Where<AuditGmsThree>,
  ): Promise<Count> {
    return this.auditGmsThreeRepository.updateAll(auditGmsThree, where);
  }

  @get('/audit-gms-threes/{id}')
  @response(200, {
    description: 'AuditGmsThree model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AuditGmsThree, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AuditGmsThree, {exclude: 'where'}) filter?: FilterExcludingWhere<AuditGmsThree>
  ): Promise<AuditGmsThree> {
    return this.auditGmsThreeRepository.findById(id, filter);
  }

  @patch('/audit-gms-threes/{id}')
  @response(204, {
    description: 'AuditGmsThree PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsThree, {partial: true}),
        },
      },
    })
    auditGmsThree: AuditGmsThree,
  ): Promise<void> {
    await this.auditGmsThreeRepository.updateById(id, auditGmsThree);
  }

  @put('/audit-gms-threes/{id}')
  @response(204, {
    description: 'AuditGmsThree PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() auditGmsThree: AuditGmsThree,
  ): Promise<void> {
    await this.auditGmsThreeRepository.replaceById(id, auditGmsThree);
  }

  @del('/audit-gms-threes/{id}')
  @response(204, {
    description: 'AuditGmsThree DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.auditGmsThreeRepository.deleteById(id);
  }
}
