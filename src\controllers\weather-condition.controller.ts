import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {WeatherCondition} from '../models';
import {WeatherConditionRepository} from '../repositories';

export class WeatherConditionController {
  constructor(
    @repository(WeatherConditionRepository)
    public weatherConditionRepository : WeatherConditionRepository,
  ) {}

  @post('/weather-conditions')
  @response(200, {
    description: 'WeatherCondition model instance',
    content: {'application/json': {schema: getModelSchemaRef(WeatherCondition)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(WeatherCondition, {
            title: 'NewWeatherCondition',
            exclude: ['id'],
          }),
        },
      },
    })
    weatherCondition: Omit<WeatherCondition, 'id'>,
  ): Promise<WeatherCondition> {
    return this.weatherConditionRepository.create(weatherCondition);
  }

  @get('/weather-conditions/count')
  @response(200, {
    description: 'WeatherCondition model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(WeatherCondition) where?: Where<WeatherCondition>,
  ): Promise<Count> {
    return this.weatherConditionRepository.count(where);
  }

  @get('/weather-conditions')
  @response(200, {
    description: 'Array of WeatherCondition model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(WeatherCondition, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(WeatherCondition) filter?: Filter<WeatherCondition>,
  ): Promise<WeatherCondition[]> {
    return this.weatherConditionRepository.find(filter);
  }

  @patch('/weather-conditions')
  @response(200, {
    description: 'WeatherCondition PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(WeatherCondition, {partial: true}),
        },
      },
    })
    weatherCondition: WeatherCondition,
    @param.where(WeatherCondition) where?: Where<WeatherCondition>,
  ): Promise<Count> {
    return this.weatherConditionRepository.updateAll(weatherCondition, where);
  }

  @get('/weather-conditions/{id}')
  @response(200, {
    description: 'WeatherCondition model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(WeatherCondition, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(WeatherCondition, {exclude: 'where'}) filter?: FilterExcludingWhere<WeatherCondition>
  ): Promise<WeatherCondition> {
    return this.weatherConditionRepository.findById(id, filter);
  }

  @patch('/weather-conditions/{id}')
  @response(204, {
    description: 'WeatherCondition PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(WeatherCondition, {partial: true}),
        },
      },
    })
    weatherCondition: WeatherCondition,
  ): Promise<void> {
    await this.weatherConditionRepository.updateById(id, weatherCondition);
  }

  @put('/weather-conditions/{id}')
  @response(204, {
    description: 'WeatherCondition PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() weatherCondition: WeatherCondition,
  ): Promise<void> {
    await this.weatherConditionRepository.replaceById(id, weatherCondition);
  }

  @del('/weather-conditions/{id}')
  @response(204, {
    description: 'WeatherCondition DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.weatherConditionRepository.deleteById(id);
  }
}
