import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ConstructionActivity} from '../models';
import {ConstructionActivityRepository} from '../repositories';

export class ConstructionActivityController {
  constructor(
    @repository(ConstructionActivityRepository)
    public constructionActivityRepository : ConstructionActivityRepository,
  ) {}

  @post('/construction-activities')
  @response(200, {
    description: 'ConstructionActivity model instance',
    content: {'application/json': {schema: getModelSchemaRef(ConstructionActivity)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ConstructionActivity, {
            title: 'NewConstructionActivity',
            exclude: ['id'],
          }),
        },
      },
    })
    constructionActivity: Omit<ConstructionActivity, 'id'>,
  ): Promise<ConstructionActivity> {
    return this.constructionActivityRepository.create(constructionActivity);
  }

  @get('/construction-activities/count')
  @response(200, {
    description: 'ConstructionActivity model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ConstructionActivity) where?: Where<ConstructionActivity>,
  ): Promise<Count> {
    return this.constructionActivityRepository.count(where);
  }

  @get('/construction-activities')
  @response(200, {
    description: 'Array of ConstructionActivity model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ConstructionActivity, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ConstructionActivity) filter?: Filter<ConstructionActivity>,
  ): Promise<ConstructionActivity[]> {
    return this.constructionActivityRepository.find(filter);
  }

  @patch('/construction-activities')
  @response(200, {
    description: 'ConstructionActivity PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ConstructionActivity, {partial: true}),
        },
      },
    })
    constructionActivity: ConstructionActivity,
    @param.where(ConstructionActivity) where?: Where<ConstructionActivity>,
  ): Promise<Count> {
    return this.constructionActivityRepository.updateAll(constructionActivity, where);
  }

  @get('/construction-activities/{id}')
  @response(200, {
    description: 'ConstructionActivity model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ConstructionActivity, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ConstructionActivity, {exclude: 'where'}) filter?: FilterExcludingWhere<ConstructionActivity>
  ): Promise<ConstructionActivity> {
    return this.constructionActivityRepository.findById(id, filter);
  }

  @patch('/construction-activities/{id}')
  @response(204, {
    description: 'ConstructionActivity PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ConstructionActivity, {partial: true}),
        },
      },
    })
    constructionActivity: ConstructionActivity,
  ): Promise<void> {
    await this.constructionActivityRepository.updateById(id, constructionActivity);
  }

  @put('/construction-activities/{id}')
  @response(204, {
    description: 'ConstructionActivity PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() constructionActivity: ConstructionActivity,
  ): Promise<void> {
    await this.constructionActivityRepository.replaceById(id, constructionActivity);
  }

  @del('/construction-activities/{id}')
  @response(204, {
    description: 'ConstructionActivity DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.constructionActivityRepository.deleteById(id);
  }
}
