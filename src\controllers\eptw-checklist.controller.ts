import {

  Filter,

  repository,

} from '@loopback/repository';
import {

  param,
  get,
  getModelSchemaRef,
  response,
} from '@loopback/rest';
import { EptwChecklist } from '../models';
import { EptwChecklistRepository } from '../repositories';

export class EptwChecklistController {
  constructor(
    @repository(EptwChecklistRepository)
    public eptwChecklistRepository: EptwChecklistRepository,
  ) { }

  @get('/eptw-checklists')
  @response(200, {
    description: 'Array of EptwChecklist model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EptwChecklist, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(EptwChecklist) filter?: Filter<EptwChecklist>,
  ): Promise<EptwChecklist[]> {

    const data = await this.eptwChecklistRepository.find();

    // Modify the response: Map data, set id sequentially starting from 1
    const modifiedData = data.map((item, index) => {
      const newItem = new EptwChecklist({
        ...item,
        id: index + 1  // Assigning sequential IDs starting from 1
      });
      return newItem;
    });

    console.log(modifiedData);  // Debugging log to verify changes

    return modifiedData;
  }


}
