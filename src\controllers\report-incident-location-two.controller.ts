import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  LocationTwo,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentLocationTwoController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationTwo)},
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<LocationTwo> {
    return this.reportIncidentRepository.locationTwo(id);
  }
}
