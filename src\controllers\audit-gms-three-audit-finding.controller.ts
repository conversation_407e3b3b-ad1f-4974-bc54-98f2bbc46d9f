import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AuditGmsThree,
  AuditFinding,
} from '../models';
import {AuditGmsThreeRepository} from '../repositories';

export class AuditGmsThreeAuditFindingController {
  constructor(
    @repository(AuditGmsThreeRepository) protected auditGmsThreeRepository: AuditGmsThreeRepository,
  ) { }

  @get('/audit-gms-threes/{id}/audit-findings', {
    responses: {
      '200': {
        description: 'Array of AuditGmsThree has many AuditFinding',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AuditFinding)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<AuditFinding>,
  ): Promise<AuditFinding[]> {
    return this.auditGmsThreeRepository.auditFindings(id).find(filter);
  }

  @post('/audit-gms-threes/{id}/audit-findings', {
    responses: {
      '200': {
        description: 'AuditGmsThree model instance',
        content: {'application/json': {schema: getModelSchemaRef(AuditFinding)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof AuditGmsThree.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditFinding, {
            title: 'NewAuditFindingInAuditGmsThree',
            exclude: ['id'],
            optional: ['auditGmsThreeId']
          }),
        },
      },
    }) auditFinding: Omit<AuditFinding, 'id'>,
  ): Promise<AuditFinding> {
    return this.auditGmsThreeRepository.auditFindings(id).create(auditFinding);
  }

  @patch('/audit-gms-threes/{id}/audit-findings', {
    responses: {
      '200': {
        description: 'AuditGmsThree.AuditFinding PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditFinding, {partial: true}),
        },
      },
    })
    auditFinding: Partial<AuditFinding>,
    @param.query.object('where', getWhereSchemaFor(AuditFinding)) where?: Where<AuditFinding>,
  ): Promise<Count> {
    return this.auditGmsThreeRepository.auditFindings(id).patch(auditFinding, where);
  }

  @del('/audit-gms-threes/{id}/audit-findings', {
    responses: {
      '200': {
        description: 'AuditGmsThree.AuditFinding DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(AuditFinding)) where?: Where<AuditFinding>,
  ): Promise<Count> {
    return this.auditGmsThreeRepository.auditFindings(id).delete(where);
  }
}
