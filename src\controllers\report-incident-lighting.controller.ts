import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  Lighting,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentLightingController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/lighting', {
    responses: {
      '200': {
        description: 'Lighting belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Lighting)},
          },
        },
      },
    },
  })
  async getLighting(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<Lighting> {
    return this.reportIncidentRepository.lighting(id);
  }
}
