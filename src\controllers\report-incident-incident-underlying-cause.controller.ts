import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  IncidentUnderlyingCause,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentIncidentUnderlyingCauseController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/incident-underlying-cause', {
    responses: {
      '200': {
        description: 'IncidentUnderlyingCause belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentUnderlyingCause)},
          },
        },
      },
    },
  })
  async getIncidentUnderlyingCause(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<IncidentUnderlyingCause> {
    return this.reportIncidentRepository.incidentUnderlyingCause(id);
  }
}
