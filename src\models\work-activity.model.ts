import { Entity, model, property, hasMany } from '@loopback/repository';
import { ObservationReport } from './observation-report.model';

@model()
export class WorkActivity extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;
  
  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @hasMany(() => ObservationReport)
  observationReports: ObservationReport[];

  constructor(data?: Partial<WorkActivity>) {
    super(data);
  }
}

export interface WorkActivityRelations {
  // describe navigational properties here
}

export type WorkActivityWithRelations = WorkActivity & WorkActivityRelations;
