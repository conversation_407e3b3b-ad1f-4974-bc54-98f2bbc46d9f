import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationFour,
  PermitReport,
} from '../models';
import {LocationFourRepository} from '../repositories';

export class LocationFourPermitReportController {
  constructor(
    @repository(LocationFourRepository) protected locationFourRepository: LocationFourRepository,
  ) { }

  @get('/location-fours/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'Array of LocationFour has many PermitReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(PermitReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    return this.locationFourRepository.permitReports(id).find(filter);
  }

  @post('/location-fours/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationFour model instance',
        content: {'application/json': {schema: getModelSchemaRef(PermitReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationFour.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {
            title: 'NewPermitReportInLocationFour',
            exclude: ['id'],
            optional: ['locationFourId']
          }),
        },
      },
    }) permitReport: Omit<PermitReport, 'id'>,
  ): Promise<PermitReport> {
    return this.locationFourRepository.permitReports(id).create(permitReport);
  }

  @patch('/location-fours/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationFour.PermitReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: Partial<PermitReport>,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.locationFourRepository.permitReports(id).patch(permitReport, where);
  }

  @del('/location-fours/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationFour.PermitReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.locationFourRepository.permitReports(id).delete(where);
  }
}
