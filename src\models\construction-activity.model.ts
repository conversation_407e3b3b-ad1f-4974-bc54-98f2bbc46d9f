import {Entity, model, property} from '@loopback/repository';

@model()
export class ConstructionActivity extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  locationOneId?: string;

  @property({
    type: 'string',
  })
  locationTwoId?: string;

  @property({
    type: 'string',
  })
  locationThreeId?: string;

  @property({
    type: 'string',
  })
  locationFourId?: string;

  @property({
    type: 'string',
  })
  locationFiveId?: string;

  @property({
    type: 'string',
  })
  locationSixId?: string;

  @property({
    type: 'string',
  })
  permitStartDate?: string;

  @property({
    type: 'string',
  })
  permitEndDate?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  assessorId?: string;

  @property({
    type: 'string',
  })
  approverID?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  permits?: object[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  checklist?: object[];

  @property({
    type: 'string',
  })
  applicantSign?: string;

  @property({
    type: 'string',
  })
  assessorSign?: string;

  @property({
    type: 'string',
  })
  approverSign?: string;

  @property({
    type: 'string',
  })
  permitType?: string;


  constructor(data?: Partial<ConstructionActivity>) {
    super(data);
  }
}

export interface ConstructionActivityRelations {
  // describe navigational properties here
}

export type ConstructionActivityWithRelations = ConstructionActivity & ConstructionActivityRelations;
