import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationSix, LocationSixRelations, ObservationReport, PermitReport} from '../models';
import {ObservationReportRepository} from './observation-report.repository';
import {PermitReportRepository} from './permit-report.repository';

export class LocationSixRepository extends DefaultCrudRepository<
  LocationSix,
  typeof LocationSix.prototype.id,
  LocationSixRelations
> {

  public readonly observationReports: HasManyRepositoryFactory<ObservationReport, typeof LocationSix.prototype.id>;

  public readonly permitReports: HasManyRepositoryFactory<PermitReport, typeof LocationSix.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('ObservationReportRepository') protected observationReportRepositoryGetter: Getter<ObservationReportRepository>, @repository.getter('PermitReportRepository') protected permitReportRepositoryGetter: Getter<PermitReportRepository>,
  ) {
    super(LocationSix, dataSource);
    this.permitReports = this.createHasManyRepositoryFactoryFor('permitReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('permitReports', this.permitReports.inclusionResolver);
    this.observationReports = this.createHasManyRepositoryFactoryFor('observationReports', observationReportRepositoryGetter,);
    this.registerInclusionResolver('observationReports', this.observationReports.inclusionResolver);
  }
}
