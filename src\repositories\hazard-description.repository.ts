import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {HazardDescription, HazardDescriptionRelations} from '../models';

export class HazardDescriptionRepository extends DefaultCrudRepository<
  HazardDescription,
  typeof HazardDescription.prototype.id,
  HazardDescriptionRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(HazardDescription, dataSource);
  }
}
