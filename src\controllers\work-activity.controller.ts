import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { WorkActivity } from '../models';
import { WorkActivityRepository } from '../repositories';
import { CognitoJwtVerifier } from "aws-jwt-verify";
import { authenticate } from '@loopback/authentication';

// @authenticate('jwt')
export class WorkActivityController {
  constructor(
    @repository(WorkActivityRepository)
    public workActivityRepository: WorkActivityRepository,
  ) { }


  // @get('/work-activities/verify/{token}')
  // @response(200, {
  //   description: 'WorkActivity model instance',
  //   content: {
  //     'application/json': {
  //       schema: getModelSchemaRef(WorkActivity, {includeRelations: true}),
  //     },
  //   },
  // })
  // async verifyToken(


  // ): Promise<any> {
  //   const verifier = CognitoJwtVerifier.create({
  //     userPoolId: `${process.env.AWS_USER_POOL_ID}`,
  //     tokenUse: "access",
  //     clientId: `${process.env.AWS_CLIENT_ID}`
  //   });

  //   try {
  //     const payload = await verifier.verify(
  //      'eyJraWQiOiJPNHVTb... // the JWT as string
  //     );
  //     console.log("Token is valid. Payload:", payload);
  //   } catch {
  //     console.log("Token not valid!");
  //   }
  //   // return this.workActivityRepository.findById(id, filter);
  // }

  @post('/work-activities')
  @response(200, {
    description: 'WorkActivity model instance',
    content: { 'application/json': { schema: getModelSchemaRef(WorkActivity) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(WorkActivity, {
            title: 'NewWorkActivity',
            exclude: ['id'],
          }),
        },
      },
    })
    workActivity: Omit<WorkActivity, 'id'>,
  ): Promise<WorkActivity> {
    return this.workActivityRepository.create(workActivity);
  }

  @get('/work-activities/count')
  @response(200, {
    description: 'WorkActivity model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(WorkActivity) where?: Where<WorkActivity>,
  ): Promise<Count> {
    return this.workActivityRepository.count(where);
  }

  @get('/work-activities')
  @response(200, {
    description: 'Array of WorkActivity model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              title: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(WorkActivity) filter?: Filter<WorkActivity>,
  ): Promise<{ id: string; title: string }[]> {
    const workActivities = await this.workActivityRepository.find(filter);

    // Transform workActivities into the desired format
    const transformedWorkActivities = workActivities.map(workActivity => ({
      id: workActivity.id ?? '',
      title: workActivity.name ?? '',
    }));

    return transformedWorkActivities;
  }

  @patch('/work-activities')
  @response(200, {
    description: 'WorkActivity PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(WorkActivity, { partial: true }),
        },
      },
    })
    workActivity: WorkActivity,
    @param.where(WorkActivity) where?: Where<WorkActivity>,
  ): Promise<Count> {
    return this.workActivityRepository.updateAll(workActivity, where);
  }

  @get('/work-activities/{id}')
  @response(200, {
    description: 'WorkActivity model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(WorkActivity, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(WorkActivity, { exclude: 'where' }) filter?: FilterExcludingWhere<WorkActivity>
  ): Promise<WorkActivity> {
    return this.workActivityRepository.findById(id, filter);
  }

  @patch('/work-activities/{id}')
  @response(204, {
    description: 'WorkActivity PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(WorkActivity, { partial: true }),
        },
      },
    })
    workActivity: WorkActivity,
  ): Promise<void> {
    await this.workActivityRepository.updateById(id, workActivity);
  }

  @put('/work-activities/{id}')
  @response(204, {
    description: 'WorkActivity PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() workActivity: WorkActivity,
  ): Promise<void> {
    await this.workActivityRepository.replaceById(id, workActivity);
  }

  @del('/work-activities/{id}')
  @response(204, {
    description: 'WorkActivity DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.workActivityRepository.deleteById(id);
  }
}
