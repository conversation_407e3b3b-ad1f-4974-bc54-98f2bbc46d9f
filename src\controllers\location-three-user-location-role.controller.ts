import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationThree,
  UserLocationRole,
} from '../models';
import {LocationThreeRepository} from '../repositories';

export class LocationThreeUserLocationRoleController {
  constructor(
    @repository(LocationThreeRepository) protected locationThreeRepository: LocationThreeRepository,
  ) { }

  @get('/location-threes/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'Array of LocationThree has many UserLocationRole',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UserLocationRole)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<UserLocationRole>,
  ): Promise<UserLocationRole[]> {
    return this.locationThreeRepository.userLocationRoles(id).find(filter);
  }

  @post('/location-threes/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationThree model instance',
        content: {'application/json': {schema: getModelSchemaRef(UserLocationRole)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationThree.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, {
            title: 'NewUserLocationRoleInLocationThree',
            exclude: ['id'],
            optional: ['locationThreeId']
          }),
        },
      },
    }) userLocationRole: Omit<UserLocationRole, 'id'>,
  ): Promise<UserLocationRole> {
    return this.locationThreeRepository.userLocationRoles(id).create(userLocationRole);
  }

  @patch('/location-threes/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationThree.UserLocationRole PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, {partial: true}),
        },
      },
    })
    userLocationRole: Partial<UserLocationRole>,
    @param.query.object('where', getWhereSchemaFor(UserLocationRole)) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.locationThreeRepository.userLocationRoles(id).patch(userLocationRole, where);
  }

  @del('/location-threes/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationThree.UserLocationRole DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(UserLocationRole)) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.locationThreeRepository.userLocationRoles(id).delete(where);
  }
}
