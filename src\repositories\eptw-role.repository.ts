import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {EptwRole, EptwRoleRelations} from '../models';

export class EptwRoleRepository extends DefaultCrudRepository<
  EptwRole,
  typeof EptwRole.prototype.id,
  EptwRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(EptwRole, dataSource);
  }
}
