import { inject } from '@loopback/core';
import { DefaultCrudRepository } from '@loopback/repository';
import { MongoDataSource } from '../datasources';
import { Action, ActionRelations } from '../models';
import moment from 'moment';

export class ActionRepository extends DefaultCrudRepository<
  Action,
  typeof Action.prototype.id,
  ActionRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(Action, dataSource);
    this.modelClass.observe('before save', async (ctx) => {
      const now = moment().toISOString();

      if (ctx.instance) {
        // If it's a new instance, set createdDate
        if (ctx.isNewInstance) {
          ctx.instance.createdDate = now;
        }
        // Update the updatedDate field for all save operations
        ctx.instance.updatedDate = now;
      } else if (ctx.data) {
        // If updating an existing instance, set only updatedDate
        ctx.data.updatedDate = now;
      }
    });
  }
}
