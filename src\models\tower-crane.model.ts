import {Entity, model, property} from '@loopback/repository';

@model()
export class TowerCrane extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  locationOneId?: string;

  @property({
    type: 'string',
  })
  locationTwoId?: string;

  @property({
    type: 'string',
  })
  locationThreeId?: string;

  @property({
    type: 'string',
  })
  locationFourId?: string;

  @property({
    type: 'string',
  })
  locationFiveId?: string;

  @property({
    type: 'string',
  })
  locationSixId?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  type?: string;

  @property({
    type: 'string',
  })
  contractor?: string;

  @property({
    type: 'string',
  })
  contractorContact?: string;

  @property({
    type: 'string',
  })
  contractorManager?: string;

  @property({
    type: 'string',
  })
  applicantName?: string;

  @property({
    type: 'string',
  })
  ContractorManagerContact?: string;

  @property({
    type: 'string',
  })
  craneCoordinator?: string;

  @property({
    type: 'string',
  })
  craneCoordinatorContact?: string;

  @property({
    type: 'string',
  })
  craneId?: string;

  @property({
    type: 'string',
  })
  craneType?: string;

  @property({
    type: 'string',
  })
  craneLocation?: string;

  @property({
    type: 'string',
  })
  serviceRecords?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  applicantPart1?: object[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  applicantPart2?: object[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  assessorPart1?: object[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  assessorPart2?: object[];

  @property({
    type: 'any',
  })
  status?: any;

  @property({
    type: 'string',
  })
  applicantId?: string;

  @property({
    type: 'string',
  })
  permitType?: string;

  @property({
    type: 'string',
  })
  applicantSign?: string;

  @property({
    type: 'string',
  })
  assessorSign?: string;

  @property({
    type: 'string',
  })
  assessorId?: string;

  @property({
    type: 'string',
  })
  approverID?: string;

  @property({
    type: 'string',
  })
  permitStartDate?: string;

  @property({
    type: 'string',
  })
  permitEndDate?: string;


  constructor(data?: Partial<TowerCrane>) {
    super(data);
  }
}

export interface TowerCraneRelations {
  // describe navigational properties here
}

export type TowerCraneWithRelations = TowerCrane & TowerCraneRelations;
