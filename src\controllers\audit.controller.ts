import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { Audit, Action } from '../models';
import { AuditRepository, ActionRepository, UserRepository, UserLocationRoleRepository, LocationOneRepository, LocationTwoRepository, LocationThreeRepository, LocationFourRepository, AuditFindingRepository } from '../repositories';
import moment from 'moment';
import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';
import { v4 as uuidv4 } from 'uuid';
import { LocationFilterService } from '../services';


@authenticate('jwt')
export class AuditController {
  constructor(
    @repository(AuditRepository)
    public auditRepository: AuditRepository,
    @repository(AuditFindingRepository)
    public auditFindingRepository: AuditFindingRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @inject('services.LocationFilterService')
    public locationFilterService: LocationFilterService,

  ) { }

  @post('/audits')
  @response(200, {
    description: 'Audit model instance',
    content: { 'application/json': { schema: getModelSchemaRef(Audit) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Audit, {
            title: 'NewAudit',
            exclude: ['id'],
          }),
        },
      },
    })
    audit: Omit<Audit, 'id'>,
  ): Promise<Audit> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const count = await this.auditRepository.count();
    audit.maskId = `GMS-${moment().format('YYMMDD')}-${String(count.count + 1).padStart(4, '0')}`;

    const existingAudits = await this.auditRepository.find({
      where: {
        assignedToId: audit.assignedToId,
        status: { nin: ['Submitted', 'Cancelled'] },
      },
    });

    // Convert input audit dates to moment objects
    const auditStart = moment(audit.dateTime, 'DD/MM/YYYY', true);
    const auditEnd = moment(audit.endDateTime, 'DD/MM/YYYY', true);

    if (!auditStart.isValid() || !auditEnd.isValid()) {
      throw new HttpErrors.BadRequest('Invalid date format. Please use DD/MM/YYYY.');
    }

    // Filter audits that overlap with the new audit's timeframe
    const conflictingAudit = existingAudits.find(existingAudit => {
      const existingStart = moment(existingAudit.dateTime, ['DD/MM/YYYY', moment.ISO_8601], true);
      const existingEnd = moment(existingAudit.endDateTime, ['DD/MM/YYYY', moment.ISO_8601], true);

      return (
        existingStart.isValid() &&
        existingEnd.isValid() &&
        (
          auditStart.isBetween(existingStart, existingEnd, null, '[]') ||  // New start overlaps existing
          auditEnd.isBetween(existingStart, existingEnd, null, '[]') ||    // New end overlaps existing
          (auditStart.isSameOrBefore(existingStart) && auditEnd.isSameOrAfter(existingEnd)) // New audit fully contains an existing one
        )
      );
    });

    if (conflictingAudit) {
      throw new HttpErrors.Conflict('The requested audit time clashes with an existing audit.');
    }

    const auditData = await this.auditRepository.create(audit);
    let actionItem = {
      application: "Audit",
      actionType: "audit",

      description: "",
      dueDate: audit.dateTime,

      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: auditData.id,
      submittedById: user?.id,
      assignedToId: audit.assignedToId
    }
    await this.actionRepository.create(actionItem)

    return auditData
  }

  @get('/audits/count')
  @response(200, {
    description: 'Audit model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(Audit) where?: Where<Audit>,
  ): Promise<Count> {
    return this.auditRepository.count(where);
  }

  @get('/my-audits')
  @response(200, {
    description: 'Array of Audit model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Audit, { includeRelations: true }),
        },
      },
    },
  })
  async findMyAudits(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Audit) filter?: Filter<Audit>,
  ): Promise<Audit[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (user)
      return this.auditRepository.find({ ...filter, where: { assignedToId: user.id } });
    else
      return []
  }


  @get('/all-audits')
  @response(200, {
    description: 'Array of Audit model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Audit, { includeRelations: true }),
        },
      },
    },
  })
  async findAllAudits(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Audit) filter?: Filter<Audit>,
  ): Promise<Audit[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {

      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId

      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);

      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };
      return this.auditRepository.find({
        ...filter, where: whereClause
      });
    }
    else {
      throw new Error('Unauthorized')
    }
  }

  @get('/audits')
  @response(200, {
    description: 'Array of Audit model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Audit, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Audit) filter?: Filter<Audit>,
  ): Promise<Audit[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId
      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);
      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };

      const audits = await this.auditRepository.find({
        ...filter,
        where: whereClause
      });

      const modifiedAudits = await Promise.all(
        audits.map(async (data) => {
          const auditFindings = await this.auditFindingRepository.find({
            where: { auditId: data.id }
          });

          // Then fetch actions for each audit finding and include the auditFindings data in each action
          const actionsPromises = auditFindings.map(async (finding) => {
            const actions = await this.actionRepository.find({ where: { objectId: finding.id } });
            // Map each action to include the auditFinding data under applicationDetails
            return actions.map(action => ({
              ...action,
              applicationDetails: finding
            }));
          });
          const actionsForFindings = await Promise.all(actionsPromises);

          // Flatten the array of actions arrays
          const totalActions = actionsForFindings.flat();

          // Filter actions to find those that are completed
          const completedActions = totalActions.filter(action => action.status === 'completed');

          // Create an instance of ReportIncident with the desired properties
          const modifiedReport = new Audit({
            ...data,
            auditData: {
              ...data.auditData,
              totalActions: totalActions,
              completedActions: completedActions
            }
          });

          return modifiedReport;
        })
      );


      return modifiedAudits;

    } else {
      throw new HttpErrors.NotFound('user not found')
    }
  }

  @patch('/audits')
  @response(200, {
    description: 'Audit PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Audit, { partial: true }),
        },
      },
    })
    audit: Audit,
    @param.where(Audit) where?: Where<Audit>,
  ): Promise<Count> {
    return this.auditRepository.updateAll(audit, where);
  }

  @get('/audits/{id}')
  @response(200, {
    description: 'Audit model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Audit, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Audit, { exclude: 'where' }) filter?: FilterExcludingWhere<Audit>
  ): Promise<Audit> {


    return this.auditRepository.findById(id, filter);
  }

  @patch('/audits/{id}/{action_id}')
  @response(204, {
    description: 'Audit PATCH success',
  })
  async updateAuditById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Audit, { partial: true }),
        },
      },
    })
    audit: Audit,
  ): Promise<void> {
    let status = '';
    let actionItem = [];
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (audit.actions && audit.actions.length > 0) {
      status = 'Done. (Open Actions)';
      actionItem = audit.actions.map((action: any) => {
        {
          return {
            application: "Audit",
            actionType: "aud_take_actions_control",
            description: `${uuidv4()}`,
            actionToBeTaken: action.actionToBeTaken,
            dueDate: action.dueDate,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: action.actionOwner

          }
        }
      }
      )
      await this.actionRepository.createAll(actionItem)
    } else {
      status = 'Closed'
    }
    audit.status = status;
    await this.auditRepository.updateById(id, audit);

    await this.actionRepository.updateById(action_id, { status: 'completed' })
  }

  @patch('/audits/{id}')
  @response(204, {
    description: 'Audit PATCH success',
  })
  async updateById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Audit, { partial: true }),
        },
      },
    })
    audit: Audit,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (audit.status === 'Cancelled') {
      const auditFindings = await this.auditFindingRepository.find({ where: { auditId: id } })

      for (const auditFinding of auditFindings) {

        await this.actionRepository.deleteAll({ objectId: auditFinding.id })


      }
      await this.auditFindingRepository.deleteAll({ auditId: id });
    }
    if (audit.status === 'Submitted') {


      const auditFindings = await this.auditFindingRepository.find({ where: { auditId: id } })

      for (const auditFinding of auditFindings) {
        const actionItem = {
          application: "AuditFinding",
          actionType: "audit_take_actions",
          description: '',
          actionToBeTaken: "Please address the root cause of the above non-conformance, identify and implement corrective actions within the stipulated time period and provide documentary evidence of the same from within the portal.",
          dueDate: moment(auditFinding.dueDate, 'YYYY-MM-DD').format('DD/MM/YYYY'),
          status: 'open',
          createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
          objectId: auditFinding.id,
          submittedById: user?.id,
          assignedToId: auditFinding.assignedToId

        }
        await this.actionRepository.create(actionItem)
        const updateData = { actionAssigned: true }
        await this.auditFindingRepository.updateById(auditFinding.id, updateData);
      }
    }


    const existingAudit = await this.auditRepository.findOne({
      where: {
        and: [
          { assignedToId: audit.assignedToId },
          { dateTime: { lte: audit.endDateTime } },
          { endDateTime: { gte: audit.dateTime } },
          { status: { nin: ['Submitted', 'Cancelled'] } }
        ],
      },
    });

    if (existingAudit) {
      throw new HttpErrors.Conflict('The requested audit time clashes with an existing audit.gi');
    }


    await this.auditRepository.updateById(id, audit);
  }

  @patch('audits/actions/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateActionsById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, { partial: true }),
        },
      },
    })
    action: Action,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    // const reportData = await this.auditRepository.findById(action.objectId)
    action.status = 'submitted'
    if (action.actionType === 'aud_take_actions_control' || action.actionType === 'audit_take_actions' || action.actionType === 'aud_retake_actions') {
      delete action.actionType



      const actionItem = {
        application: "AuditFinding",
        actionType: "aud_verify_actions",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: action.assignedToId
      }



      await this.actionRepository.create(actionItem)

    }

    if (action.actionType === 'aud_verify_actions') {

      if (action.objectId) {

        const observationDetails = await this.auditRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },


          ]
        })



      }

    }

    if (action.actionType === 'reject') {

      const actionItem = {
        application: "AuditFinding",
        actionType: "aud_retake_actions",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: action.submittedById
      }

      await this.actionRepository.create(actionItem)
    }
    delete action.assignedToId
    await this.actionRepository.updateById(id, action);
  }

  @put('/audits/{id}')
  @response(204, {
    description: 'Audit PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() audit: Audit,
  ): Promise<void> {
    await this.auditRepository.replaceById(id, audit);
  }

  @del('/audits/{id}')
  @response(204, {
    description: 'Audit DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.auditRepository.deleteById(id);
  }
}

