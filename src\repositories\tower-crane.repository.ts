import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {TowerCrane, TowerCraneRelations} from '../models';

export class TowerCraneRepository extends DefaultCrudRepository<
  TowerCrane,
  typeof TowerCrane.prototype.id,
  TowerCraneRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(TowerCrane, dataSource);
  }
}
