import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {HazardType} from '../models';
import {HazardTypeRepository} from '../repositories';

export class HazardTypeController {
  constructor(
    @repository(HazardTypeRepository)
    public hazardTypeRepository : HazardTypeRepository,
  ) {}

  @post('/hazard-types')
  @response(200, {
    description: 'HazardType model instance',
    content: {'application/json': {schema: getModelSchemaRef(HazardType)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardType, {
            title: 'NewHazardType',
            exclude: ['id'],
          }),
        },
      },
    })
    hazardType: Omit<HazardType, 'id'>,
  ): Promise<HazardType> {
    return this.hazardTypeRepository.create(hazardType);
  }

  @get('/hazard-types/count')
  @response(200, {
    description: 'HazardType model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(HazardType) where?: Where<HazardType>,
  ): Promise<Count> {
    return this.hazardTypeRepository.count(where);
  }

  @get('/hazard-types')
  @response(200, {
    description: 'Array of HazardType model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(HazardType, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(HazardType) filter?: Filter<HazardType>,
  ): Promise<HazardType[]> {
    return this.hazardTypeRepository.find(filter);
  }

  @patch('/hazard-types')
  @response(200, {
    description: 'HazardType PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardType, {partial: true}),
        },
      },
    })
    hazardType: HazardType,
    @param.where(HazardType) where?: Where<HazardType>,
  ): Promise<Count> {
    return this.hazardTypeRepository.updateAll(hazardType, where);
  }

  @get('/hazard-types/{id}')
  @response(200, {
    description: 'HazardType model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(HazardType, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(HazardType, {exclude: 'where'}) filter?: FilterExcludingWhere<HazardType>
  ): Promise<HazardType> {
    return this.hazardTypeRepository.findById(id, filter);
  }

  @patch('/hazard-types/{id}')
  @response(204, {
    description: 'HazardType PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardType, {partial: true}),
        },
      },
    })
    hazardType: HazardType,
  ): Promise<void> {
    await this.hazardTypeRepository.updateById(id, hazardType);
  }

  @put('/hazard-types/{id}')
  @response(204, {
    description: 'HazardType PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() hazardType: HazardType,
  ): Promise<void> {
    await this.hazardTypeRepository.replaceById(id, hazardType);
  }

  @del('/hazard-types/{id}')
  @response(204, {
    description: 'HazardType DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.hazardTypeRepository.deleteById(id);
  }
}
