// Copyright IBM Corp. 2020. All Rights Reserved.
// Node module: loopback4-example-shopping
// This file is licensed under the MIT License.
// License text available at https://opensource.org/licenses/MIT
import {
  authenticate,
  TokenService,
  UserService
} from '@loopback/authentication';
import { TokenServiceBindings } from '@loopback/authentication-jwt';
import { authorize } from '@loopback/authorization';
import { inject } from '@loopback/core';
import { model, property, repository } from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  Request,
  requestBody,
  Response,
  response,
  RestBindings
} from '@loopback/rest';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import isemail from 'isemail';
import _ from 'lodash';
import { SentMessageInfo } from 'nodemailer';
import { PasswordHasherBindings, UserServiceBindings } from '../keys';
import { KeyAndPassword, ResetPasswordInit, User, UserLocationRole, Action, LocationFour, LocationThree, LocationTwo, LocationOne, ReportData } from '../models';
import { Credentials, UserRepository, LocationOneRepository, ReportDataRepository, AuditRoleRepository, InspectionRoleRepository, ActionRepository, LocationThreeRepository, LocationFourRepository, LocationTwoRepository, UserCredentialsRepository, ReportRoleRepository, UserLocationRoleRepository, IncidentRoleRepository, EhsRoleRepository, EptwRoleRepository } from '../repositories';
import {
  basicAuthorization,
  PasswordHasher,

  UserManagementService,
  validateCredentials,
  validateKeyPassword
} from '../services';
import { OPERATION_SECURITY_SPEC } from '../utils';
import {
  CredentialsRequestBody,
  PasswordResetRequestBody,
  UserProfileSchema
} from './specs/user-controller.specs';
import axios from 'axios';
import { CognitoIdentityServiceProvider, SNS } from 'aws-sdk';
import { SqsService } from '../services/sqs-service.service';
import multer from 'multer';
import FormData from 'form-data';
import moment from 'moment';

const storage = multer.memoryStorage();
const upload = multer({ storage });

const cognito = new CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_COGNITO_ACCESS_KEY,
  secretAccessKey: process.env.AWS_COGNITO_SECRET_KEY
})

@model()
export class NewUserRequest extends User {
  @property({
    type: 'string',
    required: true,
  })
  password: string;
}

interface Details {
  [key: string]: string; // This allows any string as a property name with a string value
  grant_type: string;
  client_id: string;
  client_secret: string;
  username: string;
  password: string;
}

interface LocationDetail {
  locationOne: string;
  locationTwo: string;
  locationThree: string;
  locationFour: string;
}

export class DeviceTokenRequest {
  @property({
    type: 'string',
    required: true,
  })
  deviceToken: string;
}

export class UserManagementController {
  constructor(
    @repository(ReportDataRepository)
    public reportDataRepository: ReportDataRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(IncidentRoleRepository)
    public incidentRoleRepository: IncidentRoleRepository,
    @repository(EhsRoleRepository)
    public ehsRoleRepository: EhsRoleRepository,
    @repository(EptwRoleRepository)
    public eptwRoleRepository: EptwRoleRepository,
    @repository(ReportRoleRepository)
    public reportRoleRepository: ReportRoleRepository,
    @repository(AuditRoleRepository)
    public auditRoleRepository: AuditRoleRepository,
    @repository(InspectionRoleRepository)
    public inspectionRoleRepository: InspectionRoleRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(UserCredentialsRepository)
    public userCredentialRepository: UserCredentialsRepository,
    @inject(PasswordHasherBindings.PASSWORD_HASHER)
    public passwordHasher: PasswordHasher,
    @inject(TokenServiceBindings.TOKEN_SERVICE)
    public jwtService: TokenService,
    @inject(UserServiceBindings.USER_SERVICE)
    public userService: UserService<User, Credentials>,
    @inject(UserServiceBindings.USER_SERVICE)
    public userManagementService: UserManagementService,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @inject(RestBindings.Http.RESPONSE) private res: Response
  ) {


  }

  @get('/users/test', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },

  })
  @authenticate('jwt')
  async testNotification(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<any> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) { return this.sqsService.sendMessage(user, 'Test Title', 'Test Message from API') } else { throw new HttpErrors.NotFound(`User not found. Try again`); }



  }

  @post('/reportBug')
  async reportBug(
    @requestBody() requestBody: {
      desc: string,
      accessToken: string,
    },
  ): Promise<void> {
    const { desc, accessToken } = requestBody;

    const data = {
      short_description: desc,
      description: desc,
      urgency: "2",
      impact: "3",
      category: "EHS",
      subcategory: "EHS"
    };

    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      }
    };

    const SERVICE_NOW_BUG_URL = `${process.env.SERVICE_BUG_URL}`; // Replace this with your actual URL

    try {
      const response = await axios.post(SERVICE_NOW_BUG_URL, data, config);
      return response.data
      // Implement additional logic based on response if needed
    } catch (error) {
      console.error('Error reporting the bug:', error);
      throw new HttpErrors.BadRequest('Failed to report bug due to an external service error');
    }
  }

  @post('/uploadToServiceNow', {
    responses: {
      200: {
        description: 'File uploaded to ServiceNow',
        content: {
          'application/json': {
            schema: {
              type: 'object',
            },
          },
        },
      },
    },
  })
  async uploadToServiceNow(
    @requestBody.file() request: Request,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ): Promise<object> {
    // Call multer manually to handle file upload
    return new Promise<object>((resolve, reject) => {
      upload.single('file')(request, response, async (err) => {
        if (err) return reject(err);

        // The file is available in req.file, and other form fields are available in req.body
        const file = request.file;
        const incidentSysId = request.body.sys_id;

        if (!file) {
          reject('No file was uploaded.');
        }

        const formData = new FormData();
        formData.append('uploadFile', file?.buffer, file?.originalname);

        // Your ServiceNow instance details
        const serviceNowUrl = `${process.env.SERVICE_BASE_URL}/api/now/attachment/file?table_name=incident&table_sys_id=${incidentSysId}&file_name=${file?.originalname}`;

        // Securely get this from your environment variables or configuration
        const details: Details = {
          grant_type: 'password',
          client_id: `${process.env.SERVICE_CLIENT_ID}`,
          client_secret: `${process.env.SERVICE_CLIENT_SECRET}`,
          username: `${process.env.SERVICE_USERNAME}`,
          password: `${process.env.SERVICE_PASSWORD}`,
        };


        const formBody = Object.keys(details)
          .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(details[key]))
          .join('&');

        try {
          const response = await axios.post(`${process.env.SERVICE_URL}`, formBody, {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          });
          const token = response.data.access_token;

          try {
            const serviceNowResponse = await axios.post(serviceNowUrl, formData, {
              headers: {
                ...formData.getHeaders(),
                'Authorization': `Bearer ${token}`
              },
              maxContentLength: Infinity,
              maxBodyLength: Infinity
            });

            resolve(serviceNowResponse.data);
          } catch (error) {
            reject(error);
          }
        } catch (error) {
          console.log(error)
          throw new HttpErrors.InternalServerError('Failed to obtain OAuth token');
        }


      });
    });
  }

  @get('/oauth-token')
  async getOAuthToken(): Promise<object> {
    const details: Details = {
      grant_type: 'password',
      client_id: `${process.env.SERVICE_CLIENT_ID}`,
      client_secret: `${process.env.SERVICE_CLIENT_SECRET}`,
      username: `${process.env.SERVICE_USERNAME}`,
      password: `${process.env.SERVICE_PASSWORD}`,
    };

    console.log(details)
    const formBody = Object.keys(details)
      .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(details[key]))
      .join('&');
    console.log(formBody)
    try {
      const response = await axios.post(`${process.env.SERVICE_URL}`, formBody, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      return response.data;
    } catch (error) {
      console.log(error)
      throw new HttpErrors.InternalServerError('Failed to obtain OAuth token');
    }
  }

  @post('/users', {
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewUserRequest, {
            title: 'NewUser',
          }),
        },
      },
    })
    newUserRequest: NewUserRequest,
  ): Promise<User> {
    // All new users have the "customer" role by default
    newUserRequest.roles = ['user'];
    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));

    try {
      newUserRequest.resetKey = '';
      return await this.userManagementService.createUser(newUserRequest);
    } catch (error) {
      // MongoError 11000 duplicate key
      if (error.code === 11000 && error.errmsg.includes('index: uniqueEmail')) {
        throw new HttpErrors.Conflict('Email value is already taken');
      } else {
        throw error;
      }
    }
  }


  @post('/users/external', {
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async createExternal(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewUserRequest, {
            title: 'NewUser',
          }),
        },
      },
    })
    newUserRequest: NewUserRequest,
  ): Promise<User> {
    // All new users have the "customer" role by default
    newUserRequest.roles = ['user'];
    newUserRequest.type = 'External';
    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));

    try {
      newUserRequest.resetKey = '';

      const params = {
        UserPoolId: `${process.env.AWS_USER_POOL_ID}`,
        Username: newUserRequest.email,
        TemporaryPassword: newUserRequest.password,
        MessageAction: 'SUPPRESS',
        UserAttributes: [
          {
            Name: 'email',
            Value: newUserRequest.email
          },
          {
            Name: 'email_verified',
            Value: 'true'
          }
        ]
      };

      const awsNewUser = await cognito.adminCreateUser(params).promise();
      console.log(awsNewUser)
      if (awsNewUser.User?.Username) {
        newUserRequest.id = awsNewUser.User.Username;
        return await this.userManagementService.createUser(newUserRequest);
      } else {
        throw new HttpErrors.NotFound(`User not created. Try again`);
      }

    } catch (error) {
      // MongoError 11000 duplicate key
      if (error.code === 11000 && error.errmsg.includes('index: uniqueEmail')) {
        throw new HttpErrors.Conflict('Email value is already taken');
      } else {
        throw error;
      }
    }
  }

  @del('/users/{id}')
  @response(204, {
    description: 'User DELETE success',
  })
  @authenticate('jwt')
  async deleteUser(@param.path.string('id') id: string): Promise<void> {
    const user = await this.userRepository.findById(id);

    if (!user) {
      throw new HttpErrors.NotFound(`User with id ${id} not found.`);
    }

    await this.userRepository.delete(user);

  }
  @patch('/users/{userId}', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')

  async set(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('userId') userId: string,
    @requestBody({ description: 'update user' }) user: User,
  ): Promise<void> {
    try {
      // Only admin can assign roles

      // console.log(user)
      return await this.userRepository.updateById(userId, user);
    } catch (e) {
      // console.log(e)
      return e;
    }
  }

  @get('/users/{userId}', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')

  async findById(@param.path.string('userId') userId: string): Promise<User> {
    return this.userRepository.findById(userId);
  }




  @get('/users', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async find(): Promise<User[]> {
    return this.userRepository.find();
  }


  // 644acfc949e6a60b6940f99e for action plan reviewer
  @authenticate('jwt')
  @get('/action-assignee-list')
  async getActionAssignee(

  ): Promise<any> {
    const users = await this.userRepository.find()
    const id = "644acfd149e6a60b6940f99f";

    const filteredUsers = users.filter(user => {
      return user.customRoles?.ehs?.includes(id);
    }).map(user => {
      return {
        id: user.id,
        title: user.firstName
      };
    });

    return filteredUsers;

  }


  @post('/all-users-by-location', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findAllUsersByLocation(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {


    let whereCondition = {


      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]

    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/action-assignee-list', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findActionAssignee(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "644acfd149e6a60b6940f99f";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @post('/lead-investigator-list', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findLeadInvestigator(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "651b170229324caa869d6841";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @authenticate('jwt')
  @get('/eptw-general-list')
  async getPtwGeneral(

  ): Promise<any> {
    const users = await this.userRepository.find()
    const id = "644acfd149e6a60b6940f99f";

    const filteredUsers = users.filter(user => {
      return user.customRoles?.ehs?.includes(id);
    }).map(user => {
      return {
        id: user.id,
        title: user.firstName
      };
    });

    return filteredUsers;

  }


  @authenticate('jwt')
  @get('/action-reviewer-list')
  async getActionReviewer(

  ): Promise<any> {
    const users = await this.userRepository.find()
    const id = "644acfc949e6a60b6940f99e";

    const filteredUsers = users.filter(user => {
      return user.customRoles?.ehs?.includes(id);
    }).map(user => {
      return {
        id: user.id,
        title: user.firstName
      };
    });

    return filteredUsers;

  }

  @post('/action-reviewer-list', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findActionReviewer(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "644acfc949e6a60b6940f99e";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @post('/incident-owner-list', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findIncidentOwner(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
              level: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId', 'level'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
      level: string;
    }
  ): Promise<User[]> {

    let roleId = "";
    switch (payload.level) {
      case '1': roleId = "64b7766dd5c7a6b02233901c"; break;
      case '2': roleId = "64b7766dd5c7a6b02233901c"; break;
      case '3': roleId = "64b77484d5c7a6b022339016"; break;
      case '4': roleId = "64b7756cd5c7a6b022339018"; break;
      case '5': roleId = "64b77582d5c7a6b022339019"; break;
      case '0': roleId = "661777f6dffb4b9f734ffc22"; break;

      default: throw new HttpErrors.NotFound('Invalid Impact Classification');
    }

    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/eptw-high-risk-approver', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findHighRiskApprover(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64a3db4b9e7163b6c2e5ab3b";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/eptw-dcso-approver', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findDcsoApprover(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64a3db559e7163b6c2e5ab3c";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @post('/users/inspector', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findInspector(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "654d03b6275bbc6e5b9a57fc";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }



  @post('/users/audit_action_plan_reviewer', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findAuditActionPlanReviewer(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "654d03fa275bbc6e5b9a57ff";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @post('/users/inspection_action_plan_implementor', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findInspectionActionPlanImplementor(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "654d03ee275bbc6e5b9a57fe";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }



  @post('/users/inspection_action_plan_reviewer', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findInspectionActionPlanReviewer(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "654d03fa275bbc6e5b9a57ff";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/eptw-construction-assessor', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findConstructionAssessor(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
              isWah: { type: 'boolean' },
              isConfinedSpace: { type: 'boolean' },
              isLifting: { type: 'boolean' },
              isHighRisk: { type: 'boolean' }
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
      isWah: boolean;
      isConfinedSpace: boolean;
      isLifting: boolean;

    }
  ): Promise<User[]> {


    const wahRoleId = "644b27e641cb57116200a88e";
    const confinedSpaceRoleId = "644b287241cb57116200a88f";
    const liftingRoleId = "64741c0447efd939764b3f23";
    const allLocationId = 'all';

    let roleIds: string[] = ["64741c2347efd939764b3f24"];
    if (payload.isWah) {
      roleIds.push(wahRoleId);
    }
    if (payload.isConfinedSpace) {
      roleIds.push(confinedSpaceRoleId);
    }
    if (payload.isLifting) {
      roleIds.push(liftingRoleId);
    }

    console.log(roleIds);
    const andConditions = roleIds.map(roleId => ({ roles: { inq: [roleId] } }));
    let whereCondition = {
      and: andConditions,
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    console.log(userLocationRoles)


    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @get('/users/me', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async getCurrentUser(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<any> {
    // (@jannyHou)FIXME: explore a way to generate OpenAPI schema
    // for symbol property

    // console.log(currentUserProfile)
    const email = currentUserProfile.email;
    // const user = await this.userRepository.findOne({ where: { email: email } });
    const user = await this.userRepository.findOne({ where: { email }, include: [{ relation: 'userLocation' }] });
    if (!user) {
      throw new HttpErrors.Unauthorized('UnAuthorized');
    }

    const userRoles = await this.userLocationRoleRepository.find({ where: { userId: user.id } });

    const uniqueRolesArray = Array.from(new Set(userRoles.map(role => role.roles).flat()));

    const countryRoles = await this.locationOneRepository.find({
      where: { id: { inq: uniqueRolesArray } },
      fields: {
        id: true,
        name: true,
      }
    });

    const ehsRoles = await this.ehsRoleRepository.find({
      where: { id: { inq: uniqueRolesArray } },
      fields: {
        id: true,
        name: true,
      }
    });

    const incidentRoles = await this.incidentRoleRepository.find({
      where: { id: { inq: uniqueRolesArray } },
      fields: {
        id: true,
        name: true,
      }
    });

    const eptwRoles = await this.eptwRoleRepository.find({
      where: { id: { inq: uniqueRolesArray } },
      fields: {
        id: true,
        name: true,
      }
    });

    const reportRoles = await this.reportRoleRepository.find({
      where: { id: { inq: uniqueRolesArray } },
      fields: {
        id: true,
        name: true,
      }
    });

    const auditRoles = await this.auditRoleRepository.find({
      where: { id: { inq: uniqueRolesArray } },
      fields: {
        id: true,
        name: true,
      }
    });

    const inspectionRoles = await this.inspectionRoleRepository.find({
      where: { id: { inq: uniqueRolesArray } },
      fields: {
        id: true,
        name: true,
      }
    });


    const combinedRoles = [...countryRoles, ...incidentRoles, ...ehsRoles, ...eptwRoles, ...reportRoles, ...auditRoles, ...inspectionRoles];

    return { ...user, validationRoles: combinedRoles };


  }

  @post('/users/me', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },

  })
  @authenticate('jwt')
  async printCurrentUser(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DeviceTokenRequest, {
            title: 'NewUser',
          }),
        },
      },
    })
    deviceTokenRequest: DeviceTokenRequest,
  ): Promise<any> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email }, include: [{ relation: 'userLocation' }] });
    if (user) {

      if (!user.deviceToken || user.deviceToken !== deviceTokenRequest.deviceToken) {

        // Replace with your platform application ARN
        const sns = new SNS({
          region: process.env.AWS_REGION,
          accessKeyId: process.env.AWS_ACCESS_KEY_ID,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
        });
        try {
          const createEndpointResponse = await sns
            .createPlatformEndpoint({
              PlatformApplicationArn: `${process.env.AWS_SNS_TOPIC_ARN}`,
              Token: deviceTokenRequest.deviceToken
            })
            .promise();

          const endpointArn = createEndpointResponse.EndpointArn;

          await this.userRepository.updateById(user?.id, { deviceToken: deviceTokenRequest.deviceToken, arn: endpointArn })
        }
        catch (error) {
          throw new Error('Failed to create endpoint');
        }

      }

      return user;
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized');
    }

  }


  @post('/users/login', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  })
  async login(
    @requestBody(CredentialsRequestBody) credentials: Credentials,
  ): Promise<{ token: string }> {
    // ensure the user exists, and the password is correct
    const user = await this.userService.verifyCredentials(credentials);

    // convert a User object into a UserProfile object (reduced set of properties)
    const userProfile = this.userService.convertToUserProfile(user);

    // create a JSON Web Token based on the user profile
    const token = await this.jwtService.generateToken(userProfile);

    return { token };
  }

  @put('/users/forgot-password', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The updated user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async forgotPassword(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody(PasswordResetRequestBody) credentials: Credentials,
  ): Promise<{ token: string }> {
    const { email, password } = credentials;
    const { id } = currentUserProfile;

    const user = await this.userRepository.findById(id);

    if (!user) {
      throw new HttpErrors.NotFound('User account not found');
    }

    if (email !== user?.email) {
      throw new HttpErrors.Forbidden('Invalid email address');
    }

    validateCredentials(_.pick(credentials, ['email', 'password']));

    const passwordHash = await this.passwordHasher.hashPassword(password);

    await this.userRepository
      .userCredentials(user.id)
      .patch({ password: passwordHash });

    const userProfile = this.userService.convertToUserProfile(user);

    const token = await this.jwtService.generateToken(userProfile);

    return { token };
  }

  @post('/users/reset-password/init', {
    responses: {
      '200': {
        description: 'Confirmation that reset password email has been sent',
      },
    },
  })
  async resetPasswordInit(
    @requestBody() resetPasswordInit: ResetPasswordInit,
  ): Promise<string> {
    if (!isemail.validate(resetPasswordInit.email)) {
      throw new HttpErrors.UnprocessableEntity('Invalid email address');
    }

    const sentMessageInfo: SentMessageInfo =
      await this.userManagementService.requestPasswordReset(
        resetPasswordInit.email,
      );

    if (sentMessageInfo.accepted.length) {
      return 'Successfully sent reset password link';
    }
    throw new HttpErrors.InternalServerError(
      'Error sending reset password email',
    );
  }

  @put('/users/reset-password/finish', {
    responses: {
      '200': {
        description: 'A successful password reset response',
      },
    },
  })
  async resetPasswordFinish(
    @requestBody() keyAndPassword: KeyAndPassword,
  ): Promise<string> {
    validateKeyPassword(keyAndPassword);

    const foundUser = await this.userRepository.findOne({
      where: { resetKey: keyAndPassword.resetKey },
    });

    if (!foundUser) {
      throw new HttpErrors.NotFound(
        'No associated account for the provided reset key',
      );
    }

    const user = await this.userManagementService.validateResetKeyLifeSpan(
      foundUser,
    );

    const passwordHash = await this.passwordHasher.hashPassword(
      keyAndPassword.password,
    );

    try {
      await this.userRepository
        .userCredentials(user.id)
        .patch({ password: passwordHash });

      await this.userRepository.updateById(user.id, user);
    } catch (e) {
      return e;
    }

    return 'Password reset successful';
  }

  @post('/users/get_users', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findUsers(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
              mode: { type: 'string' }
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId', 'mode'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
      mode: string;
    }
  ): Promise<User[]> {

    let roleId = '';
    switch (payload.mode) {

      case 'incident-reviewer':
        roleId = "64a526ea9e7163b6c2e5ab3f";
        break;
      case 'inspection-assignor':
        roleId = "654d0392275bbc6e5b9a57fb";
        break;

      case 'inspector':
        roleId = "654d03b6275bbc6e5b9a57fc";
        break;

      case 'inspection-action-plan-implementor':
        roleId = "654d03ee275bbc6e5b9a57fe";
        break;

      case 'inspection-action-plan-reviewer':
        roleId = "654d03fa275bbc6e5b9a57ff";
        break;
      case 'audit-assignor':
        roleId = "66710ddd364b0d05af9bed2d";
        break;
      case 'auditor':
        roleId = "66710deb364b0d05af9bed2e";
        break;
      case 'root-cause-investigator':
        roleId = "66710df8364b0d05af9bed2f";
        break;
      case 'audit-action-plan-implementor':
        roleId = "66710e19364b0d05af9bed30";
        break;
      case 'audit-action-plan-reviewer':
        roleId = "66710e2a364b0d05af9bed31";
        break;
      case 'qro-action-assignor':
        roleId = "673fe74d9e33816c979466e8";
        break;
      case 'obs-action-assignee':
        roleId = "644acfd149e6a60b6940f99f";
        break;
      case 'obs-action-reviewer':
        roleId = "644acfc949e6a60b6940f99e";
        break;
      case 'statistics-reviewer':
        roleId = "67bf50d0cea75b683db127ac";
        break;



      default:

        throw new Error('Type is missing')
    }



    const whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @get('/users/location-fours', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findLocationFoursOfUsers(): Promise<any> {

    const roleId = '651a68e829324caa869d6840';
    const dueMonth = moment().subtract(1, 'months').format('MMM YYYY');


    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: { roles: { inq: [[roleId]] } },
    });

    const groupedByUserId = userLocationRoles.reduce((acc, curr) => {
      if (!acc[curr.userId]) {
        acc[curr.userId] = [];
      }
      acc[curr.userId].push({
        locationOneId: curr.locationOneId,
        locationTwoId: curr.locationTwoId,
        locationThreeId: curr.locationThreeId,
        locationFourId: curr.locationFourId,
      });
      return acc;
    }, {} as Record<string, { locationOneId: string, locationTwoId: string, locationThreeId: string, locationFourId: string }[]>);

    const result = [];
    for (const userId in groupedByUserId) {
      const locations = groupedByUserId[userId];
      const locationFours = await this.getLocationFoursForUser(locations);
      const actionItems = await this.createActionItemsForUser(locationFours, userId, dueMonth);
      result.push({ userId, actionItems });
    }

    return result;




  }

  async getLocationFoursForUser(locations: { locationOneId: string, locationTwoId: string, locationThreeId: string, locationFourId: string }[]): Promise<LocationFour[]> {
    const locationFourIds = new Set<string>();

    for (const location of locations) {
      if (location.locationFourId && location.locationFourId !== 'tier4-all') {
        locationFourIds.add(location.locationFourId);
      } else if (location.locationThreeId && location.locationThreeId !== 'tier3-all') {
        const locationFours = await this.locationFourRepository.find({ where: { locationThreeId: location.locationThreeId } });
        locationFours.forEach(lf => {
          if (lf.id) {
            locationFourIds.add(lf.id);
          }
        });
      } else if (location.locationTwoId && location.locationTwoId !== 'tier2-all') {
        const locationThrees = await this.locationThreeRepository.find({ where: { locationTwoId: location.locationTwoId } });
        const locationThreeIds = locationThrees.map(lt => lt.id);
        const locationFours = await this.locationFourRepository.find({ where: { locationThreeId: { inq: locationThreeIds } } });
        locationFours.forEach(lf => {
          if (lf.id) {
            locationFourIds.add(lf.id);
          }
        });
      } else if (location.locationOneId && location.locationOneId !== 'tier1-all') {
        const locationTwos = await this.locationTwoRepository.find({ where: { locationOneId: location.locationOneId } });
        const locationTwoIds = locationTwos.map(lt => lt.id);
        const locationThrees = await this.locationThreeRepository.find({ where: { locationTwoId: { inq: locationTwoIds } } });
        const locationThreeIds = locationThrees.map(lt => lt.id);
        const locationFours = await this.locationFourRepository.find({ where: { locationThreeId: { inq: locationThreeIds } } });
        locationFours.forEach(lf => {
          if (lf.id) {
            locationFourIds.add(lf.id);
          }
        });
      } else if (location.locationOneId === 'tier1-all') {
        const allLocationFours = await this.locationFourRepository.find();
        allLocationFours.forEach(lf => {
          if (lf.id) {
            locationFourIds.add(lf.id);
          }
        });
      }
    }
    return this.locationFourRepository.find({
      where: {
        id: { inq: Array.from(locationFourIds) },
        /* or: [
           // Condition 1: Both startDate and endDate are undefined
           // { startDate: undefined, endDate: undefined },
 
           // Condition 2: startDate is less than or equal to the current date, and endDate is greater than or equal to >          
           {
             and: [
               { startDate: { lte: new Date().toISOString() } }, // startDate is less than or equal to the current date
               { endDate: { gte: new Date().toISOString() } },   // endDate is greater than or equal to the current date
             ],
           },
 
           // Condition 3: startDate is less than or equal to the current date, and endDate is null or undefined
           {
             and: [
               { startDate: { lte: new Date().toISOString() } }, // startDate is less than or equal to the current date
               { endDate: undefined },  // endDate is either null (undefined will also be treated as null)
             ],
           },
         ], */

      },
    });


  }

  async createActionItemsForUser(locationFours: LocationFour[], userId: string, dueMonth: string): Promise<Action[]> {
    const actionItems = [];

    for (const locationFour of locationFours) {
      // Prepare the description from the current locationFour item
      const description = locationFour.id?.toString();

      // Check if an action item with the same description, dueMonth, and assignedToId already exists
      const existingItems = await this.actionRepository.find({
        where: {
          and: [
            { description: description },
            { dueDate: dueMonth },
            { assignedToId: userId }
          ]
        }
      });

      const existingMonthlyReports = await this.reportDataRepository.find({
        where: {
          and: [
            { locationFourId: description },
            { yearAndMonth: dueMonth }
          ]
        }
      })

      // If no existing item is found, create a new one
      if (existingItems.length === 0 && existingMonthlyReports.length === 0) {
        const actionItem = new Action({
          application: "Report",
          actionType: "report",
          description: description,
          dueDate: dueMonth, 
          status: "open",
          createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
          objectId: '',
          submittedById: '',
          assignedToId: userId
        });
        const savedActionItem = await this.actionRepository.create(actionItem);
        actionItems.push(savedActionItem);
      }
      // Optionally handle else case if you need to perform any action when skipping creation
    }

    return actionItems;
  }

  @get('/find-and-delete-orphans')
  async findAndDeleteOrphans(): Promise<{ deletedLocationFours: number, deletedLocationThrees: number, deletedActions: number }> {
    // First find orphaned IDs
    const { orphanedLocationFours, orphanedLocationThrees } = await this.findOrphanedLocationIds();

    // Delete LocationFour entities
    const deletedLocationFours = await this.locationFourRepository.deleteAll({
      id: { inq: orphanedLocationFours }
    });

    // Delete LocationThree entities
    const deletedLocationThrees = await this.locationThreeRepository.deleteAll({
      id: { inq: orphanedLocationThrees }
    });

    const deletedActions = await this.actionRepository.deleteAll({
      description: { inq: orphanedLocationFours }
    })
    return {
      deletedLocationFours: deletedLocationFours.count,
      deletedLocationThrees: deletedLocationThrees.count,
      deletedActions: deletedActions.count
    };
  }

  @get('/orphaned-locations')
  async findOrphanedLocationIds(): Promise<{ orphanedLocationFours: string[], orphanedLocationThrees: string[] }> {
    const orphanedLocationFourIds = new Set<string>();
    const orphanedLocationThreeIds = new Set<string>();
    const allLocationFours = await this.locationFourRepository.find();

    for (const locationFour of allLocationFours) {
      let isOrphaned = false;
      const locationThreeId = locationFour.locationThreeId;

      // Check LocationThree
      if (locationThreeId) {
        const locationThree = await this.locationThreeRepository.findById(locationThreeId).catch(() => null);
        if (!locationThree) {
          isOrphaned = true; // Mark as orphaned if LocationThree does not exist
          orphanedLocationThreeIds.add(locationThreeId); // Collect the LocationThree ID as orphaned
        } else {
          // Check LocationTwo
          const locationTwoId = locationThree.locationTwoId;
          if (locationTwoId) {
            const locationTwo = await this.locationTwoRepository.findById(locationTwoId).catch(() => null);
            if (!locationTwo) {
              isOrphaned = true; // Propagate orphaned status if LocationTwo does not exist
            } else {
              // Check LocationOne
              const locationOneId = locationTwo.locationOneId;
              if (locationOneId) {
                const locationOne = await this.locationOneRepository.findById(locationOneId).catch(() => null);
                if (!locationOne) {
                  isOrphaned = true; // Propagate orphaned status if LocationOne does not exist
                }
              } else {
                isOrphaned = true;
              }
            }
          } else {
            isOrphaned = true;
          }
        }
      } else {
        isOrphaned = true;
      }

      // If any upper level is missing, add the LocationFour ID to the set
      if (isOrphaned) {
        orphanedLocationFourIds.add(locationFour.id ?? '');
      }
    }

    return {
      orphanedLocationFours: Array.from(orphanedLocationFourIds),
      orphanedLocationThrees: Array.from(orphanedLocationThreeIds)
    };
  }

}

