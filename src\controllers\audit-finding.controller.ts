import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { AuditFinding } from '../models';
import { AuditFindingRepository, ActionRepository, UserRepository, AuditRepository } from '../repositories';
import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';
import moment from 'moment';

@authenticate('jwt')
export class AuditFindingController {
  constructor(
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(AuditFindingRepository)
    public auditFindingRepository: AuditFindingRepository,
    @repository(AuditRepository)
    public auditRepository: AuditRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @post('/audit-findings')
  @response(200, {
    description: 'AuditFinding model instance',
    content: { 'application/json': { schema: getModelSchemaRef(AuditFinding) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditFinding, {
            title: 'NewAuditFinding',
            exclude: ['id'],
          }),
        },
      },
    })
    auditFinding: Omit<AuditFinding, 'id'>,
  ): Promise<AuditFinding> {

    let category = '';
    switch (auditFinding.category) {
      case 'Good Practices': category = 'GP'; break;
      case 'Non-Conformances': category = 'NC'; break;
      case 'Opportunity For Improvement': category = 'OI'; break;

    }

    const count = await this.auditFindingRepository.count({ category: auditFinding.category })
    if (auditFinding.category === 'Non-Conformances') {
      const audit = await this.auditRepository.findById(auditFinding.auditId)
      if (!audit) {
        throw new Error('Not Found')
      }
      const auditFindingCount = await this.auditFindingRepository.count({ category: auditFinding.category, auditId: audit.id })
      auditFinding.maskId = `${audit.maskId}-${category}-${String(auditFindingCount.count + 1).padStart(2, '0')}`;
    } else {
      auditFinding.maskId = `GMS-${category}-${moment().format('YYMMDD')}-${String(count.count + 1).padStart(4, '0')}`;
    }

    return this.auditFindingRepository.create(auditFinding);
  }

  @get('/audit-findings/count')
  @response(200, {
    description: 'AuditFinding model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(AuditFinding) where?: Where<AuditFinding>,
  ): Promise<Count> {
    return this.auditFindingRepository.count(where);
  }

  @get('/audit-findings')
  @response(200, {
    description: 'Array of AuditFinding model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AuditFinding, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(AuditFinding) filter?: Filter<AuditFinding>,
  ): Promise<AuditFinding[]> {
    return this.auditFindingRepository.find(filter);
  }

  @patch('/audit-findings')
  @response(200, {
    description: 'AuditFinding PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditFinding, { partial: true }),
        },
      },
    })
    auditFinding: AuditFinding,
    @param.where(AuditFinding) where?: Where<AuditFinding>,
  ): Promise<Count> {
    return this.auditFindingRepository.updateAll(auditFinding, where);
  }

  @get('/audit-findings/{id}')
  @response(200, {
    description: 'AuditFinding model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AuditFinding, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AuditFinding, { exclude: 'where' }) filter?: FilterExcludingWhere<AuditFinding>
  ): Promise<AuditFinding> {
    return this.auditFindingRepository.findById(id, filter);
  }

  @patch('/audit-findings/{id}')
  @response(204, {
    description: 'AuditFinding PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditFinding, { partial: true }),
        },
      },
    })
    auditFinding: AuditFinding,
  ): Promise<void> {
    await this.auditFindingRepository.updateById(id, auditFinding);
  }

  @patch('/audit-findings-assign-action/{id}')
  @response(204, {
    description: 'AuditFinding PATCH success',
  })
  async createById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            // Using additionalProperties set to true to accept any object shape
            additionalProperties: true,
          },
        },
      },
    })
    auditFinding: any,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const actionItem = {
      application: "AuditFinding",
      actionType: "audit_take_actions",
      description: '',
      actionToBeTaken: auditFinding.actionToBeTaken,
      dueDate: moment(auditFinding.dueDate, 'YYYY-MM-DD').format('DD/MM/YYYY'),
      status: 'open',
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: id,
      submittedById: user?.id,
      assignedToId: auditFinding.userId

    }
    await this.actionRepository.create(actionItem)
    const updateData = { actionAssigned: true }
    await this.auditFindingRepository.updateById(id, updateData);
  }



  @put('/audit-findings/{id}')
  @response(204, {
    description: 'AuditFinding PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() auditFinding: AuditFinding,
  ): Promise<void> {
    await this.auditFindingRepository.replaceById(id, auditFinding);
  }

  @del('/audit-findings/{id}')
  @response(204, {
    description: 'AuditFinding DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.auditFindingRepository.deleteById(id);
  }
}
