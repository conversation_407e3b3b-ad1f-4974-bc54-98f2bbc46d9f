import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Inspection,
  User,
} from '../models';
import {InspectionRepository} from '../repositories';

export class InspectionUserController {
  constructor(
    @repository(InspectionRepository)
    public inspectionRepository: InspectionRepository,
  ) { }

  @get('/inspections/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to Inspection',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof Inspection.prototype.id,
  ): Promise<User> {
    return this.inspectionRepository.assignedTo(id);
  }
}
