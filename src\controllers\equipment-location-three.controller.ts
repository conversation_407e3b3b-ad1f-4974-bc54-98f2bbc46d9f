import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Equipment,
  LocationThree,
} from '../models';
import {EquipmentRepository} from '../repositories';

export class EquipmentLocationThreeController {
  constructor(
    @repository(EquipmentRepository)
    public equipmentRepository: EquipmentRepository,
  ) { }

  @get('/equipment/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to Equipment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationThree)},
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof Equipment.prototype.id,
  ): Promise<LocationThree> {
    return this.equipmentRepository.locationThree(id);
  }
}
