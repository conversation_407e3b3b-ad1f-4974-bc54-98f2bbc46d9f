import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  IncidentCircumstanceCategory,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentIncidentCircumstanceCategoryController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/incident-circumstance-category', {
    responses: {
      '200': {
        description: 'IncidentCircumstanceCategory belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentCircumstanceCategory)},
          },
        },
      },
    },
  })
  async getIncidentCircumstanceCategory(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<IncidentCircumstanceCategory> {
    return this.reportIncidentRepository.incidentCircumstanceCategory(id);
  }
}
