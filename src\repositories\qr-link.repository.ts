import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {QrLink, QrLinkRelations} from '../models';

export class QrLinkRepository extends DefaultCrudRepository<
  QrLink,
  typeof QrLink.prototype.id,
  QrLinkRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(QrLink, dataSource);
  }
}
