import { AuthenticationStrategy, AuthenticationBindings } from '@loopback/authentication';
import { HttpErrors, Request } from '@loopback/rest';
import { securityId, UserProfile } from '@loopback/security';
import {verify, decode }  from 'jsonwebtoken';
import jwksClient from 'jwks-rsa';
import { bind } from '@loopback/core';

@bind({tags: {namespace: AuthenticationBindings.STRATEGY}})
export class CognitoJwtAuthenticationStrategy implements AuthenticationStrategy {
    name = 'cognito-jwt';

    async authenticate(request: Request): Promise<UserProfile | undefined> {
        const token = this.extractCredentials(request);
        if (!token) {
           return undefined;
        }

        const jwtKeyset = jwksClient({
            jwksUri: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.AWS_USER_POOL_ID}/.well-known/jwks.json`,
        })

        const decoded = decode(token, { complete: true });
        if (!decoded || typeof decoded === 'string' || !decoded.header) {
            return undefined;
        }

        const signingKey = await jwtKeyset.getSigningKey(decoded.header.kid);
        const publicKey = signingKey.getPublicKey();

        try {
            const userProfile = verify(token, publicKey, {
                algorithms: ['RS256'],
                audience: `${process.env.AWS_CLIENT_ID}`,
                issuer: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.AWS_USER_POOL_ID}`,
            }) as UserProfile;

            userProfile[securityId] = userProfile.sub;
            return userProfile;
        } catch (err) {
            console.log(err)
            return undefined;
        }
    }

    extractCredentials(request: Request): string {
        if (!request.headers.authorization) {
            throw new HttpErrors.Unauthorized('Authorization header is missing');
        }

        const authHeaderValue = request.headers.authorization;
        if (!authHeaderValue.startsWith('Bearer ')) {
            throw new HttpErrors.Unauthorized('Authorization header is not of type Bearer');
        }

        return authHeaderValue.replace('Bearer ', '');
    }
}
