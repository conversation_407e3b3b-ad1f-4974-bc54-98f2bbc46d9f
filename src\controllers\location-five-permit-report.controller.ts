import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationFive,
  PermitReport,
} from '../models';
import {LocationFiveRepository} from '../repositories';

export class LocationFivePermitReportController {
  constructor(
    @repository(LocationFiveRepository) protected locationFiveRepository: LocationFiveRepository,
  ) { }

  @get('/location-fives/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'Array of LocationFive has many PermitReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(PermitReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    return this.locationFiveRepository.permitReports(id).find(filter);
  }

  @post('/location-fives/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationFive model instance',
        content: {'application/json': {schema: getModelSchemaRef(PermitReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationFive.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {
            title: 'NewPermitReportInLocationFive',
            exclude: ['id'],
            optional: ['locationFiveId']
          }),
        },
      },
    }) permitReport: Omit<PermitReport, 'id'>,
  ): Promise<PermitReport> {
    return this.locationFiveRepository.permitReports(id).create(permitReport);
  }

  @patch('/location-fives/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationFive.PermitReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: Partial<PermitReport>,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.locationFiveRepository.permitReports(id).patch(permitReport, where);
  }

  @del('/location-fives/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationFive.PermitReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.locationFiveRepository.permitReports(id).delete(where);
  }
}
