import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {IncidentRole, IncidentRoleRelations} from '../models';

export class IncidentRoleRepository extends DefaultCrudRepository<
  IncidentRole,
  typeof IncidentRole.prototype.id,
  IncidentRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(IncidentRole, dataSource);
  }
}
