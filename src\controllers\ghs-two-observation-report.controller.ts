import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  GhsTwo,
  ObservationReport,
} from '../models';
import {GhsTwoRepository} from '../repositories';

export class GhsTwoObservationReportController {
  constructor(
    @repository(GhsTwoRepository) protected ghsTwoRepository: GhsTwoRepository,
  ) { }

  @get('/ghs-twos/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'Array of GhsTwo has many ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ObservationReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.ghsTwoRepository.observationReports(id).find(filter);
  }

  @post('/ghs-twos/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'GhsTwo model instance',
        content: {'application/json': {schema: getModelSchemaRef(ObservationReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof GhsTwo.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReportInGhsTwo',
            exclude: ['id'],
            optional: ['ghsTwoId']
          }),
        },
      },
    }) observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    return this.ghsTwoRepository.observationReports(id).create(observationReport);
  }

  @patch('/ghs-twos/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'GhsTwo.ObservationReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: Partial<ObservationReport>,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.ghsTwoRepository.observationReports(id).patch(observationReport, where);
  }

  @del('/ghs-twos/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'GhsTwo.ObservationReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.ghsTwoRepository.observationReports(id).delete(where);
  }
}
