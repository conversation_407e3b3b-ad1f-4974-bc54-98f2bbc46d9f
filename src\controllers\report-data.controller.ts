import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { Action, ReportData, User } from '../models';
import { ReportDataRepository, UserRepository, UserLocationRoleRepository, LocationOneRepository, LocationTwoRepository, LocationThreeRepository, LocationFourRepository, ActionRepository } from '../repositories';
import { authenticate } from '@loopback/authentication';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { inject } from '@loopback/core';
import { LocationFilterService } from '../services';
import moment from 'moment';
import { v4 as uuidv4 } from 'uuid';

@authenticate('jwt')
export class ReportDataController {
  constructor(
    @repository(ReportDataRepository)
    public reportDataRepository: ReportDataRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
    @inject('services.LocationFilterService')
    public locationFilterService: LocationFilterService,
  ) { }

  @post('/report-data')
  @response(200, {
    description: 'ReportData model instance',
    content: { 'application/json': { schema: getModelSchemaRef(ReportData) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportData, {
            title: 'NewReportData',
            exclude: ['id'],
          }),
        },
      },
    })
    reportData: Omit<ReportData, 'id'>,
  ): Promise<ReportData> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    reportData.userId = user?.id ?? '';
    reportData.createdDate = moment(new Date()).format('DD-MM-YYYY HH:mm')
    const reportInfo = await this.reportDataRepository.create(reportData);

    const roleId = '67bf50d0cea75b683db127ac'; // Only one role ID to check

    const whereCondition = {
      and: [{ roles: { inq: [roleId] } }], // Only checking one role ID
      or: [
        {
          locationOneId: { inq: [reportData.locationOneId] },
          locationTwoId: { inq: [reportData.locationTwoId] },
          locationThreeId: { inq: [reportData.locationThreeId] },
          locationFourId: { inq: [reportData.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [reportData.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [reportData.locationOneId] },
          locationTwoId: { inq: [reportData.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [reportData.locationOneId] },
          locationTwoId: { inq: [reportData.locationTwoId] },
          locationThreeId: { inq: [reportData.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });



    const userIds = userLocationRoles.map(userLocationRole => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    // Loop through all users and create action items for each
    if (reportInfo) {
      await this.actionRepository.deleteAll({
        dueDate: reportData.yearAndMonth,
        description: reportData.locationFourId,
      });
    }

    const actionItems = users.map(u => new Action({
      application: "Report",
      actionType: "report_review",
      description: reportData.locationFourId,
      dueDate: reportData.yearAndMonth,
      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: reportInfo.id,
      submittedById: reportData.userId,
      assignedToId: u.id // Assign each action to a different user
    }));

    // Save all action items in the database
    const savedActionItems = await Promise.all(actionItems.map(actionItem =>
      this.actionRepository.create(actionItem)
    ));




    return reportInfo;
  }

  @get('/report-data/count')
  @response(200, {
    description: 'ReportData model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(ReportData) where?: Where<ReportData>,
  ): Promise<Count> {
    return this.reportDataRepository.count(where);
  }

  @get('/report-data')
  @response(200, {
    description: 'Array of ReportData model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportData, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(ReportData) filter?: Filter<ReportData>,
  ): Promise<ReportData[]> {
    return this.reportDataRepository.find(filter);
  }

  @get('/my-report-data')
  @response(200, {
    description: 'Array of ReportData model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportData, { includeRelations: true }),
        },
      },
    },
  })
  async findMyData(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ReportData) filter?: Filter<ReportData>,
  ): Promise<ReportData[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (!user) {
      throw new Error('Unauthorized')
    }
    const userIdToSearch = user.id;
    const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);
    const whereClause = {
      or: filterConditions.map(andConditions => ({
        and: andConditions,
      })),
    };

    return this.reportDataRepository.find({
      ...filter,
      where: whereClause
    });
  }

  @patch('/report-data')
  @response(200, {
    description: 'ReportData PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportData, { partial: true }),
        },
      },
    })
    reportData: ReportData,
    @param.where(ReportData) where?: Where<ReportData>,
  ): Promise<Count> {
    return this.reportDataRepository.updateAll(reportData, where);
  }

  @get('/report-data/{id}')
  @response(200, {
    description: 'ReportData model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ReportData, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ReportData, { exclude: 'where' }) filter?: FilterExcludingWhere<ReportData>
  ): Promise<ReportData> {
    return this.reportDataRepository.findById(id, filter);
  }

  @patch('/report-data/{id}')
  @response(204, {
    description: 'ReportData PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportData, { partial: true }),
        },
      },
    })
    reportData: ReportData,
  ): Promise<void> {
    await this.reportDataRepository.updateById(id, reportData);
  }

  @patch('/report-data/{id}/{actionId}')
  @response(204, {
    description: 'ReportData PATCH success',
  })
  async updateActionById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportData, { partial: true }),
        },
      },
    })
    reportData: ReportData,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (!user) {
      throw new Error('Unauthorized')
    }
    reportData.reviewedDate = moment(new Date()).format('DD-MM-YYYY HH:mm');
    reportData.reviewerId = user.id;
    await this.reportDataRepository.updateById(id, reportData);
    const actionData = await this.actionRepository.findById(actionId);
    await this.actionRepository.deleteAll({ description: actionData.description, actionType: 'report_review' });
  }

  @put('/report-data/{id}')
  @response(204, {
    description: 'ReportData PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() reportData: ReportData,
  ): Promise<void> {
    await this.reportDataRepository.replaceById(id, reportData);
  }

  @del('/report-data/{id}')
  @response(204, {
    description: 'ReportData DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.reportDataRepository.deleteById(id);
  }
}
