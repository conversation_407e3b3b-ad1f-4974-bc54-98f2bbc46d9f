import {Entity, model, property} from '@loopback/repository';

@model()
export class IncidentRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  permission?: any;

  @property({
    type: 'string',
  })
  created?: string;


  constructor(data?: Partial<IncidentRole>) {
    super(data);
  }
}

export interface IncidentRoleRelations {
  // describe navigational properties here
}

export type IncidentRoleWithRelations = IncidentRole & IncidentRoleRelations;
