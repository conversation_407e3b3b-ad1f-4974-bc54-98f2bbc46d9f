import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Lighting} from '../models';
import {LightingRepository} from '../repositories';

export class LightingController {
  constructor(
    @repository(LightingRepository)
    public lightingRepository : LightingRepository,
  ) {}

  @post('/lightings')
  @response(200, {
    description: 'Lighting model instance',
    content: {'application/json': {schema: getModelSchemaRef(Lighting)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Lighting, {
            title: 'NewLighting',
            exclude: ['id'],
          }),
        },
      },
    })
    lighting: Omit<Lighting, 'id'>,
  ): Promise<Lighting> {
    return this.lightingRepository.create(lighting);
  }

  @get('/lightings/count')
  @response(200, {
    description: 'Lighting model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Lighting) where?: Where<Lighting>,
  ): Promise<Count> {
    return this.lightingRepository.count(where);
  }

  @get('/lightings')
  @response(200, {
    description: 'Array of Lighting model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Lighting, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Lighting) filter?: Filter<Lighting>,
  ): Promise<Lighting[]> {
    return this.lightingRepository.find(filter);
  }

  @patch('/lightings')
  @response(200, {
    description: 'Lighting PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Lighting, {partial: true}),
        },
      },
    })
    lighting: Lighting,
    @param.where(Lighting) where?: Where<Lighting>,
  ): Promise<Count> {
    return this.lightingRepository.updateAll(lighting, where);
  }

  @get('/lightings/{id}')
  @response(200, {
    description: 'Lighting model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Lighting, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Lighting, {exclude: 'where'}) filter?: FilterExcludingWhere<Lighting>
  ): Promise<Lighting> {
    return this.lightingRepository.findById(id, filter);
  }

  @patch('/lightings/{id}')
  @response(204, {
    description: 'Lighting PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Lighting, {partial: true}),
        },
      },
    })
    lighting: Lighting,
  ): Promise<void> {
    await this.lightingRepository.updateById(id, lighting);
  }

  @put('/lightings/{id}')
  @response(204, {
    description: 'Lighting PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() lighting: Lighting,
  ): Promise<void> {
    await this.lightingRepository.replaceById(id, lighting);
  }

  @del('/lightings/{id}')
  @response(204, {
    description: 'Lighting DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.lightingRepository.deleteById(id);
  }
}
