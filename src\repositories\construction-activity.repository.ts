import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ConstructionActivity, ConstructionActivityRelations} from '../models';

export class ConstructionActivityRepository extends DefaultCrudRepository<
  ConstructionActivity,
  typeof ConstructionActivity.prototype.id,
  ConstructionActivityRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(ConstructionActivity, dataSource);
  }
}
