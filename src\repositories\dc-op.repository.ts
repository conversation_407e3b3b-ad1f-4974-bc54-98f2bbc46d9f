import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {DcOp, DcOpRelations} from '../models';

export class DcOpRepository extends DefaultCrudRepository<
  DcOp,
  typeof DcOp.prototype.id,
  DcOpRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(DcOp, dataSource);
  }
}
