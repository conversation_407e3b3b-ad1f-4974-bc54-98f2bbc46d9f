import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationFive, LocationFiveRelations, LocationSix, ObservationReport, PermitReport} from '../models';
import {LocationSixRepository} from './location-six.repository';
import {ObservationReportRepository} from './observation-report.repository';
import {PermitReportRepository} from './permit-report.repository';

export class LocationFiveRepository extends DefaultCrudRepository<
  LocationFive,
  typeof LocationFive.prototype.id,
  LocationFiveRelations
> {

  public readonly locationSixes: HasManyRepositoryFactory<LocationSix, typeof LocationFive.prototype.id>;

  public readonly observationReports: HasManyRepositoryFactory<ObservationReport, typeof LocationFive.prototype.id>;

  public readonly permitReports: HasManyRepositoryFactory<PermitReport, typeof LocationFive.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>, @repository.getter('ObservationReportRepository') protected observationReportRepositoryGetter: Getter<ObservationReportRepository>, @repository.getter('PermitReportRepository') protected permitReportRepositoryGetter: Getter<PermitReportRepository>,
  ) {
    super(LocationFive, dataSource);
    this.permitReports = this.createHasManyRepositoryFactoryFor('permitReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('permitReports', this.permitReports.inclusionResolver);
    this.observationReports = this.createHasManyRepositoryFactoryFor('observationReports', observationReportRepositoryGetter,);
    this.registerInclusionResolver('observationReports', this.observationReports.inclusionResolver);
    this.locationSixes = this.createHasManyRepositoryFactoryFor('locationSixes', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSixes', this.locationSixes.inclusionResolver);
  }
}
