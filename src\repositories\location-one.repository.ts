import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationOne, LocationOneRelations, LocationTwo, ObservationReport, UserLocationRole, PermitReport} from '../models';
import {LocationTwoRepository} from './location-two.repository';
import {ObservationReportRepository} from './observation-report.repository';
import {UserLocationRoleRepository} from './user-location-role.repository';
import {PermitReportRepository} from './permit-report.repository';

export class LocationOneRepository extends DefaultCrudRepository<
  LocationOne,
  typeof LocationOne.prototype.id,
  LocationOneRelations
> {

  public readonly locationTwos: HasManyRepositoryFactory<LocationTwo, typeof LocationOne.prototype.id>;

  public readonly observationReports: HasManyRepositoryFactory<ObservationReport, typeof LocationOne.prototype.id>;

  public readonly userLocationRoles: HasManyRepositoryFactory<UserLocationRole, typeof LocationOne.prototype.id>;

  public readonly permitReports: HasManyRepositoryFactory<PermitReport, typeof LocationOne.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('ObservationReportRepository') protected observationReportRepositoryGetter: Getter<ObservationReportRepository>, @repository.getter('UserLocationRoleRepository') protected userLocationRoleRepositoryGetter: Getter<UserLocationRoleRepository>, @repository.getter('PermitReportRepository') protected permitReportRepositoryGetter: Getter<PermitReportRepository>,
  ) {
    super(LocationOne, dataSource);
    this.permitReports = this.createHasManyRepositoryFactoryFor('permitReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('permitReports', this.permitReports.inclusionResolver);
    this.userLocationRoles = this.createHasManyRepositoryFactoryFor('userLocationRoles', userLocationRoleRepositoryGetter,);
    this.registerInclusionResolver('userLocationRoles', this.userLocationRoles.inclusionResolver);
    this.observationReports = this.createHasManyRepositoryFactoryFor('observationReports', observationReportRepositoryGetter,);
    this.registerInclusionResolver('observationReports', this.observationReports.inclusionResolver);
    this.locationTwos = this.createHasManyRepositoryFactoryFor('locationTwos', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwos', this.locationTwos.inclusionResolver);
  }
}
