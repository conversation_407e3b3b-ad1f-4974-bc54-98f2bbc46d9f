import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  LocationFour,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentLocationFourController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationFour)},
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<LocationFour> {
    return this.reportIncidentRepository.locationFour(id);
  }
}
