import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  IncidentUnderlyingCauseDescription,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentIncidentUnderlyingCauseDescriptionController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/incident-underlying-cause-description', {
    responses: {
      '200': {
        description: 'IncidentUnderlyingCauseDescription belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentUnderlyingCauseDescription)},
          },
        },
      },
    },
  })
  async getIncidentUnderlyingCauseDescription(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<IncidentUnderlyingCauseDescription> {
    return this.reportIncidentRepository.incidentUnderlyingCauseDescription(id);
  }
}
