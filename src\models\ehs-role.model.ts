import {Entity, model, property} from '@loopback/repository';

@model()
export class EhsRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  permissions?: any;

  @property({
    type: 'date',
  })
  created?: string;


  constructor(data?: Partial<EhsRole>) {
    super(data);
  }
}

export interface EhsRoleRelations {
  // describe navigational properties here
}

export type EhsRoleWithRelations = EhsRole & EhsRoleRelations;
