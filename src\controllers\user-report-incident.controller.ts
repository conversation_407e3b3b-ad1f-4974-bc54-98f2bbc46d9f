import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  User,
  ReportIncident,
} from '../models';
import {UserRepository} from '../repositories';

export class UserReportIncidentController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
  ) { }

  @get('/users/{id}/report-incidents', {
    responses: {
      '200': {
        description: 'Array of User has many ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ReportIncident)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ReportIncident>,
  ): Promise<ReportIncident[]> {
    return this.userRepository.reportIncidents(id).find(filter);
  }

  @post('/users/{id}/report-incidents', {
    responses: {
      '200': {
        description: 'User model instance',
        content: {'application/json': {schema: getModelSchemaRef(ReportIncident)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof User.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, {
            title: 'NewReportIncidentInUser',
            exclude: ['id'],
            optional: ['userId']
          }),
        },
      },
    }) reportIncident: Omit<ReportIncident, 'id'>,
  ): Promise<ReportIncident> {
    return this.userRepository.reportIncidents(id).create(reportIncident);
  }

  @patch('/users/{id}/report-incidents', {
    responses: {
      '200': {
        description: 'User.ReportIncident PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, {partial: true}),
        },
      },
    })
    reportIncident: Partial<ReportIncident>,
    @param.query.object('where', getWhereSchemaFor(ReportIncident)) where?: Where<ReportIncident>,
  ): Promise<Count> {
    return this.userRepository.reportIncidents(id).patch(reportIncident, where);
  }

  @del('/users/{id}/report-incidents', {
    responses: {
      '200': {
        description: 'User.ReportIncident DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ReportIncident)) where?: Where<ReportIncident>,
  ): Promise<Count> {
    return this.userRepository.reportIncidents(id).delete(where);
  }
}
