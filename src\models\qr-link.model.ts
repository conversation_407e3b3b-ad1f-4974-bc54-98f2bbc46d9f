import {Entity, model, property} from '@loopback/repository';

@model()
export class QrLink extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  urlId: string;

  @property({
    type: 'boolean',
    default: false,
  })
  status?: boolean;

  @property({
    type: 'date',
  })
  created?: string;


  constructor(data?: Partial<QrLink>) {
    super(data);
  }
}

export interface QrLinkRelations {
  // describe navigational properties here
}

export type QrLinkWithRelations = QrLink & QrLinkRelations;
