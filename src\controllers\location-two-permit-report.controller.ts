import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationTwo,
  PermitReport,
} from '../models';
import {LocationTwoRepository} from '../repositories';

export class LocationTwoPermitReportController {
  constructor(
    @repository(LocationTwoRepository) protected locationTwoRepository: LocationTwoRepository,
  ) { }

  @get('/location-twos/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'Array of LocationTwo has many PermitReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(PermitReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    return this.locationTwoRepository.permitReports(id).find(filter);
  }

  @post('/location-twos/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationTwo model instance',
        content: {'application/json': {schema: getModelSchemaRef(PermitReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationTwo.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {
            title: 'NewPermitReportInLocationTwo',
            exclude: ['id'],
            optional: ['locationTwoId']
          }),
        },
      },
    }) permitReport: Omit<PermitReport, 'id'>,
  ): Promise<PermitReport> {
    return this.locationTwoRepository.permitReports(id).create(permitReport);
  }

  @patch('/location-twos/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationTwo.PermitReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: Partial<PermitReport>,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.locationTwoRepository.permitReports(id).patch(permitReport, where);
  }

  @del('/location-twos/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationTwo.PermitReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.locationTwoRepository.permitReports(id).delete(where);
  }
}
