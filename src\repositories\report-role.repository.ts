import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ReportRole, ReportRoleRelations} from '../models';

export class ReportRoleRepository extends DefaultCrudRepository<
  ReportRole,
  typeof ReportRole.prototype.id,
  ReportRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(ReportRole, dataSource);
  }
}
