import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ObservationReport, ObservationReportRelations, WorkActivity, GhsOne, GhsTwo, LocationOne, LocationTwo, LocationThree, LocationFour, LocationFive, LocationSix, Action, User, HazardCategory, HazardType, HazardDescription} from '../models';
import {WorkActivityRepository} from './work-activity.repository';
import {GhsOneRepository} from './ghs-one.repository';
import {GhsTwoRepository} from './ghs-two.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';
import {LocationFiveRepository} from './location-five.repository';
import {LocationSixRepository} from './location-six.repository';
import {ActionRepository} from './action.repository';
import {UserRepository} from './user.repository';
import {HazardCategoryRepository} from './hazard-category.repository';
import {HazardTypeRepository} from './hazard-type.repository';
import {HazardDescriptionRepository} from './hazard-description.repository';

export class ObservationReportRepository extends DefaultCrudRepository<
  ObservationReport,
  typeof ObservationReport.prototype.id,
  ObservationReportRelations
> {

  public readonly workActivity: BelongsToAccessor<WorkActivity, typeof ObservationReport.prototype.id>;

  public readonly ghsOne: BelongsToAccessor<GhsOne, typeof ObservationReport.prototype.id>;

  public readonly ghsTwo: BelongsToAccessor<GhsTwo, typeof ObservationReport.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof ObservationReport.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof ObservationReport.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof ObservationReport.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof ObservationReport.prototype.id>;

  public readonly locationFive: BelongsToAccessor<LocationFive, typeof ObservationReport.prototype.id>;

  public readonly locationSix: BelongsToAccessor<LocationSix, typeof ObservationReport.prototype.id>;

  public readonly actions: HasManyRepositoryFactory<Action, typeof ObservationReport.prototype.id>;

  public readonly submitted: BelongsToAccessor<User, typeof ObservationReport.prototype.id>;

  public readonly actionOwner: BelongsToAccessor<User, typeof ObservationReport.prototype.id>;

  public readonly reviewer: BelongsToAccessor<User, typeof ObservationReport.prototype.id>;

  public readonly qroAssignee: BelongsToAccessor<User, typeof ObservationReport.prototype.id>;

  public readonly hazardCategory: BelongsToAccessor<HazardCategory, typeof ObservationReport.prototype.id>;

  public readonly hazardType: BelongsToAccessor<HazardType, typeof ObservationReport.prototype.id>;

  public readonly hazardDescription: BelongsToAccessor<HazardDescription, typeof ObservationReport.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('WorkActivityRepository') protected workActivityRepositoryGetter: Getter<WorkActivityRepository>, @repository.getter('GhsOneRepository') protected ghsOneRepositoryGetter: Getter<GhsOneRepository>, @repository.getter('GhsTwoRepository') protected ghsTwoRepositoryGetter: Getter<GhsTwoRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>, @repository.getter('ActionRepository') protected actionRepositoryGetter: Getter<ActionRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('HazardCategoryRepository') protected hazardCategoryRepositoryGetter: Getter<HazardCategoryRepository>, @repository.getter('HazardTypeRepository') protected hazardTypeRepositoryGetter: Getter<HazardTypeRepository>, @repository.getter('HazardDescriptionRepository') protected hazardDescriptionRepositoryGetter: Getter<HazardDescriptionRepository>,
  ) {
    super(ObservationReport, dataSource);
    this.hazardDescription = this.createBelongsToAccessorFor('hazardDescription', hazardDescriptionRepositoryGetter,);
    this.registerInclusionResolver('hazardDescription', this.hazardDescription.inclusionResolver);
    this.hazardType = this.createBelongsToAccessorFor('hazardType', hazardTypeRepositoryGetter,);
    this.registerInclusionResolver('hazardType', this.hazardType.inclusionResolver);
    this.hazardCategory = this.createBelongsToAccessorFor('hazardCategory', hazardCategoryRepositoryGetter,);
    this.registerInclusionResolver('hazardCategory', this.hazardCategory.inclusionResolver);
    this.qroAssignee = this.createBelongsToAccessorFor('qroAssignee', userRepositoryGetter,);
    this.registerInclusionResolver('qroAssignee', this.qroAssignee.inclusionResolver);
    this.reviewer = this.createBelongsToAccessorFor('reviewer', userRepositoryGetter,);
    this.registerInclusionResolver('reviewer', this.reviewer.inclusionResolver);
    this.actionOwner = this.createBelongsToAccessorFor('actionOwner', userRepositoryGetter,);
    this.registerInclusionResolver('actionOwner', this.actionOwner.inclusionResolver);
    this.submitted = this.createBelongsToAccessorFor('submitted', userRepositoryGetter,);
    this.registerInclusionResolver('submitted', this.submitted.inclusionResolver);
    this.actions = this.createHasManyRepositoryFactoryFor('actions', actionRepositoryGetter,);
    this.registerInclusionResolver('actions', this.actions.inclusionResolver);
    this.locationSix = this.createBelongsToAccessorFor('locationSix', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSix', this.locationSix.inclusionResolver);
    this.locationFive = this.createBelongsToAccessorFor('locationFive', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFive', this.locationFive.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.ghsTwo = this.createBelongsToAccessorFor('ghsTwo', ghsTwoRepositoryGetter,);
    this.registerInclusionResolver('ghsTwo', this.ghsTwo.inclusionResolver);
    this.ghsOne = this.createBelongsToAccessorFor('ghsOne', ghsOneRepositoryGetter,);
    this.registerInclusionResolver('ghsOne', this.ghsOne.inclusionResolver);
    this.workActivity = this.createBelongsToAccessorFor('workActivity', workActivityRepositoryGetter,);
    this.registerInclusionResolver('workActivity', this.workActivity.inclusionResolver);
  }
}
