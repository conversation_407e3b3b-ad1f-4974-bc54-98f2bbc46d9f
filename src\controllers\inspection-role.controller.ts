import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {InspectionRole} from '../models';
import {InspectionRoleRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class InspectionRoleController {
  constructor(
    @repository(InspectionRoleRepository)
    public inspectionRoleRepository : InspectionRoleRepository,
  ) {}

  @post('/inspection-roles')
  @response(200, {
    description: 'InspectionRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(InspectionRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InspectionRole, {
            title: 'NewInspectionRole',
            exclude: ['id'],
          }),
        },
      },
    })
    inspectionRole: Omit<InspectionRole, 'id'>,
  ): Promise<InspectionRole> {
    return this.inspectionRoleRepository.create(inspectionRole);
  }

  @get('/inspection-roles/count')
  @response(200, {
    description: 'InspectionRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(InspectionRole) where?: Where<InspectionRole>,
  ): Promise<Count> {
    return this.inspectionRoleRepository.count(where);
  }

  @get('/inspection-roles')
  @response(200, {
    description: 'Array of InspectionRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(InspectionRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(InspectionRole) filter?: Filter<InspectionRole>,
  ): Promise<InspectionRole[]> {
    return this.inspectionRoleRepository.find(filter);
  }

  @patch('/inspection-roles')
  @response(200, {
    description: 'InspectionRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InspectionRole, {partial: true}),
        },
      },
    })
    inspectionRole: InspectionRole,
    @param.where(InspectionRole) where?: Where<InspectionRole>,
  ): Promise<Count> {
    return this.inspectionRoleRepository.updateAll(inspectionRole, where);
  }

  @get('/inspection-roles/{id}')
  @response(200, {
    description: 'InspectionRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(InspectionRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(InspectionRole, {exclude: 'where'}) filter?: FilterExcludingWhere<InspectionRole>
  ): Promise<InspectionRole> {
    return this.inspectionRoleRepository.findById(id, filter);
  }

  @patch('/inspection-roles/{id}')
  @response(204, {
    description: 'InspectionRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InspectionRole, {partial: true}),
        },
      },
    })
    inspectionRole: InspectionRole,
  ): Promise<void> {
    await this.inspectionRoleRepository.updateById(id, inspectionRole);
  }

  @put('/inspection-roles/{id}')
  @response(204, {
    description: 'InspectionRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() inspectionRole: InspectionRole,
  ): Promise<void> {
    await this.inspectionRoleRepository.replaceById(id, inspectionRole);
  }

  @del('/inspection-roles/{id}')
  @response(204, {
    description: 'InspectionRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.inspectionRoleRepository.deleteById(id);
  }
}
