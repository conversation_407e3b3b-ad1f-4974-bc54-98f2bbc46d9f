import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {RiskCategory} from '../models';
import {RiskCategoryRepository} from '../repositories';

export class RiskCategoryController {
  constructor(
    @repository(RiskCategoryRepository)
    public riskCategoryRepository : RiskCategoryRepository,
  ) {}

  @post('/risk-categories')
  @response(200, {
    description: 'RiskCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(RiskCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskCategory, {
            title: 'NewRiskCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    riskCategory: Omit<RiskCategory, 'id'>,
  ): Promise<RiskCategory> {
    return this.riskCategoryRepository.create(riskCategory);
  }

  @get('/risk-categories/count')
  @response(200, {
    description: 'RiskCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(RiskCategory) where?: Where<RiskCategory>,
  ): Promise<Count> {
    return this.riskCategoryRepository.count(where);
  }

  @get('/risk-categories')
  @response(200, {
    description: 'Array of RiskCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(RiskCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(RiskCategory) filter?: Filter<RiskCategory>,
  ): Promise<RiskCategory[]> {
    return this.riskCategoryRepository.find(filter);
  }

  @patch('/risk-categories')
  @response(200, {
    description: 'RiskCategory PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskCategory, {partial: true}),
        },
      },
    })
    riskCategory: RiskCategory,
    @param.where(RiskCategory) where?: Where<RiskCategory>,
  ): Promise<Count> {
    return this.riskCategoryRepository.updateAll(riskCategory, where);
  }

  @get('/risk-categories/{id}')
  @response(200, {
    description: 'RiskCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(RiskCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(RiskCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<RiskCategory>
  ): Promise<RiskCategory> {
    return this.riskCategoryRepository.findById(id, filter);
  }

  @patch('/risk-categories/{id}')
  @response(204, {
    description: 'RiskCategory PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskCategory, {partial: true}),
        },
      },
    })
    riskCategory: RiskCategory,
  ): Promise<void> {
    await this.riskCategoryRepository.updateById(id, riskCategory);
  }

  @put('/risk-categories/{id}')
  @response(204, {
    description: 'RiskCategory PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() riskCategory: RiskCategory,
  ): Promise<void> {
    await this.riskCategoryRepository.replaceById(id, riskCategory);
  }

  @del('/risk-categories/{id}')
  @response(204, {
    description: 'RiskCategory DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.riskCategoryRepository.deleteById(id);
  }
}
