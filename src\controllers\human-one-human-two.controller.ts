import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  HumanOne,
  HumanTwo,
} from '../models';
import {HumanOneRepository} from '../repositories';

export class HumanOneHumanTwoController {
  constructor(
    @repository(HumanOneRepository) protected humanOneRepository: HumanOneRepository,
  ) { }

  @get('/human-ones/{id}/human-twos', {
    responses: {
      '200': {
        description: 'Array of HumanOne has many HumanTwo',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(HumanTwo)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<HumanTwo>,
  ): Promise<HumanTwo[]> {
    return this.humanOneRepository.humanTwos(id).find(filter);
  }

  @post('/human-ones/{id}/human-twos', {
    responses: {
      '200': {
        description: 'HumanOne model instance',
        content: {'application/json': {schema: getModelSchemaRef(HumanTwo)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof HumanOne.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanTwo, {
            title: 'NewHumanTwoInHumanOne',
            exclude: ['id'],
            optional: ['humanOneId']
          }),
        },
      },
    }) humanTwo: Omit<HumanTwo, 'id'>,
  ): Promise<HumanTwo> {
    return this.humanOneRepository.humanTwos(id).create(humanTwo);
  }

  @patch('/human-ones/{id}/human-twos', {
    responses: {
      '200': {
        description: 'HumanOne.HumanTwo PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanTwo, {partial: true}),
        },
      },
    })
    humanTwo: Partial<HumanTwo>,
    @param.query.object('where', getWhereSchemaFor(HumanTwo)) where?: Where<HumanTwo>,
  ): Promise<Count> {
    return this.humanOneRepository.humanTwos(id).patch(humanTwo, where);
  }

  @del('/human-ones/{id}/human-twos', {
    responses: {
      '200': {
        description: 'HumanOne.HumanTwo DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(HumanTwo)) where?: Where<HumanTwo>,
  ): Promise<Count> {
    return this.humanOneRepository.humanTwos(id).delete(where);
  }
}
