import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {QrLink} from '../models';
import {QrLinkRepository} from '../repositories';

export class QrLinkController {
  constructor(
    @repository(QrLinkRepository)
    public qrLinkRepository : QrLinkRepository,
  ) {}

  @post('/qr-links')
  @response(200, {
    description: 'QrLink model instance',
    content: {'application/json': {schema: getModelSchemaRef(QrLink)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QrLink, {
            title: 'NewQrLink',
            exclude: ['id'],
          }),
        },
      },
    })
    qrLink: Omit<QrLink, 'id'>,
  ): Promise<QrLink> {
    return this.qrLinkRepository.create(qrLink);
  }

  @get('/qr-links/count')
  @response(200, {
    description: 'QrLink model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(QrLink) where?: Where<QrLink>,
  ): Promise<Count> {
    return this.qrLinkRepository.count(where);
  }

  @get('/qr-links')
  @response(200, {
    description: 'Array of QrLink model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QrLink, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(QrLink) filter?: Filter<QrLink>,
  ): Promise<QrLink[]> {
    return this.qrLinkRepository.find(filter);
  }

  @patch('/qr-links')
  @response(200, {
    description: 'QrLink PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QrLink, {partial: true}),
        },
      },
    })
    qrLink: QrLink,
    @param.where(QrLink) where?: Where<QrLink>,
  ): Promise<Count> {
    return this.qrLinkRepository.updateAll(qrLink, where);
  }

  @get('/qr-links/{id}')
  @response(200, {
    description: 'QrLink model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QrLink, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(QrLink, {exclude: 'where'}) filter?: FilterExcludingWhere<QrLink>
  ): Promise<QrLink> {
    return this.qrLinkRepository.findById(id, filter);
  }

  @patch('/qr-links/{id}')
  @response(204, {
    description: 'QrLink PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QrLink, {partial: true}),
        },
      },
    })
    qrLink: QrLink,
  ): Promise<void> {
    await this.qrLinkRepository.updateById(id, qrLink);
  }

  @put('/qr-links/{id}')
  @response(204, {
    description: 'QrLink PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() qrLink: QrLink,
  ): Promise<void> {
    await this.qrLinkRepository.replaceById(id, qrLink);
  }

  @del('/qr-links/{id}')
  @response(204, {
    description: 'QrLink DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.qrLinkRepository.deleteById(id);
  }
}
