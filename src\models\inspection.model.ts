import {Entity, model, property, belongsTo, hasMany} from '@loopback/repository';
import {Checklist} from './checklist.model';
import {User} from './user.model';
import {LocationOne} from './location-one.model';
import {LocationTwo} from './location-two.model';
import {LocationThree} from './location-three.model';
import {LocationFour} from './location-four.model';
import {Action} from './action.model';

@model()
export class Inspection extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  month?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  startDateTime?: string;

  @property({
    type: 'string',
  })
  dateTime?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  year?: string;

  @property({
    type: 'any',
  })
  checklistReport?: any;

  @property({
    type: 'any',
  })
  inspectionData?: any;

  @property({
    type: 'any',
  })
  postActions?: any;

  @property({
    type: 'string',
  })
  remarks?: string;

  @property({
    type: 'number',
  })
  trackNo?: number;

  @belongsTo(() => Checklist)
  checklistId: string;

  @belongsTo(() => User)
  assignedToId: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @hasMany(() => Action, {keyTo: 'objectId'})
  actions: Action[];

  @belongsTo(() => User)
  approverId: string;

  @belongsTo(() => User)
  assignedById: string;

  constructor(data?: Partial<Inspection>) {
    super(data);
  }
}

export interface InspectionRelations {
  // describe navigational properties here
}

export type InspectionWithRelations = Inspection & InspectionRelations;
