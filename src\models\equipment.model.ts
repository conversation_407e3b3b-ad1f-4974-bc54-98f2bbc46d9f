import {Entity, model, property, belongsTo} from '@loopback/repository';
import {LocationOne} from './location-one.model';
import {LocationTwo} from './location-two.model';
import {LocationThree} from './location-three.model';
import {LocationFour} from './location-four.model';

@model()
export class Equipment extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  newDate?: string;

  @property({
    type: 'string',
  })
  installedDate?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  modelNumber?: string;

  @property({
    type: 'string',
  })
  serialNumber?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  testCase?: object[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  spares?: object[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  documents?: object[];

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  constructor(data?: Partial<Equipment>) {
    super(data);
  }
}

export interface EquipmentRelations {
  // describe navigational properties here
}

export type EquipmentWithRelations = Equipment & EquipmentRelations;
