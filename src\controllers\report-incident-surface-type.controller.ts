import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  SurfaceType,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentSurfaceTypeController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/surface-type', {
    responses: {
      '200': {
        description: 'SurfaceType belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SurfaceType)},
          },
        },
      },
    },
  })
  async getSurfaceType(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<SurfaceType> {
    return this.reportIncidentRepository.surfaceType(id);
  }
}
