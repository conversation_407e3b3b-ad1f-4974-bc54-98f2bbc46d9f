import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ServiceNow} from '../models';
import {ServiceNowRepository} from '../repositories';

export class ServiceNowController {
  constructor(
    @repository(ServiceNowRepository)
    public serviceNowRepository : ServiceNowRepository,
  ) {}

  @post('/service-nows')
  @response(200, {
    description: 'ServiceNow model instance',
    content: {'application/json': {schema: getModelSchemaRef(ServiceNow)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ServiceNow, {
            title: 'NewServiceNow',
            exclude: ['id'],
          }),
        },
      },
    })
    serviceNow: Omit<ServiceNow, 'id'>,
  ): Promise<ServiceNow> {
    return this.serviceNowRepository.create(serviceNow);
  }

  @get('/service-nows/count')
  @response(200, {
    description: 'ServiceNow model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ServiceNow) where?: Where<ServiceNow>,
  ): Promise<Count> {
    return this.serviceNowRepository.count(where);
  }

  @get('/service-nows')
  @response(200, {
    description: 'Array of ServiceNow model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ServiceNow, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ServiceNow) filter?: Filter<ServiceNow>,
  ): Promise<ServiceNow[]> {
    return this.serviceNowRepository.find(filter);
  }

  @patch('/service-nows')
  @response(200, {
    description: 'ServiceNow PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ServiceNow, {partial: true}),
        },
      },
    })
    serviceNow: ServiceNow,
    @param.where(ServiceNow) where?: Where<ServiceNow>,
  ): Promise<Count> {
    return this.serviceNowRepository.updateAll(serviceNow, where);
  }

  @get('/service-nows/{id}')
  @response(200, {
    description: 'ServiceNow model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ServiceNow, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ServiceNow, {exclude: 'where'}) filter?: FilterExcludingWhere<ServiceNow>
  ): Promise<ServiceNow> {
    return this.serviceNowRepository.findById(id, filter);
  }

  @patch('/service-nows/{id}')
  @response(204, {
    description: 'ServiceNow PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ServiceNow, {partial: true}),
        },
      },
    })
    serviceNow: ServiceNow,
  ): Promise<void> {
    await this.serviceNowRepository.updateById(id, serviceNow);
  }

  @put('/service-nows/{id}')
  @response(204, {
    description: 'ServiceNow PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() serviceNow: ServiceNow,
  ): Promise<void> {
    await this.serviceNowRepository.replaceById(id, serviceNow);
  }

  @del('/service-nows/{id}')
  @response(204, {
    description: 'ServiceNow DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.serviceNowRepository.deleteById(id);
  }
}
