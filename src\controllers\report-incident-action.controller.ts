import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  ReportIncident,
  Action,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentActionController {
  constructor(
    @repository(ReportIncidentRepository) protected reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/actions', {
    responses: {
      '200': {
        description: 'Array of ReportIncident has many Action',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Action)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Action>,
  ): Promise<Action[]> {
    return this.reportIncidentRepository.actions(id).find(filter);
  }

  @post('/report-incidents/{id}/actions', {
    responses: {
      '200': {
        description: 'ReportIncident model instance',
        content: {'application/json': {schema: getModelSchemaRef(Action)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {
            title: 'NewActionInReportIncident',
            exclude: ['id'],
            optional: ['objectId']
          }),
        },
      },
    }) action: Omit<Action, 'id'>,
  ): Promise<Action> {
    return this.reportIncidentRepository.actions(id).create(action);
  }

  @patch('/report-incidents/{id}/actions', {
    responses: {
      '200': {
        description: 'ReportIncident.Action PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {partial: true}),
        },
      },
    })
    action: Partial<Action>,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.reportIncidentRepository.actions(id).patch(action, where);
  }

  @del('/report-incidents/{id}/actions', {
    responses: {
      '200': {
        description: 'ReportIncident.Action DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.reportIncidentRepository.actions(id).delete(where);
  }
}
