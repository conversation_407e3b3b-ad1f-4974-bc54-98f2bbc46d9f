import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationSix,
  ObservationReport,
} from '../models';
import {LocationSixRepository} from '../repositories';

export class LocationSixObservationReportController {
  constructor(
    @repository(LocationSixRepository) protected locationSixRepository: LocationSixRepository,
  ) { }

  @get('/location-sixes/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'Array of LocationSix has many ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ObservationReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.locationSixRepository.observationReports(id).find(filter);
  }

  @post('/location-sixes/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationSix model instance',
        content: {'application/json': {schema: getModelSchemaRef(ObservationReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationSix.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReportInLocationSix',
            exclude: ['id'],
            optional: ['locationSixId']
          }),
        },
      },
    }) observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    return this.locationSixRepository.observationReports(id).create(observationReport);
  }

  @patch('/location-sixes/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationSix.ObservationReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: Partial<ObservationReport>,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationSixRepository.observationReports(id).patch(observationReport, where);
  }

  @del('/location-sixes/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationSix.ObservationReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationSixRepository.observationReports(id).delete(where);
  }
}
