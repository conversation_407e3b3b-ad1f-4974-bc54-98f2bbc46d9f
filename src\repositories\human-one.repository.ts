import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {HumanOne, HumanOneRelations, HumanTwo} from '../models';
import {HumanTwoRepository} from './human-two.repository';

export class HumanOneRepository extends DefaultCrudRepository<
  HumanOne,
  typeof HumanOne.prototype.id,
  HumanOneRelations
> {

  public readonly humanTwos: HasManyRepositoryFactory<HumanTwo, typeof HumanOne.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('HumanTwoRepository') protected humanTwoRepositoryGetter: Getter<HumanTwoRepository>,
  ) {
    super(HumanOne, dataSource);
    this.humanTwos = this.createHasManyRepositoryFactoryFor('humanTwos', humanTwoRepositoryGetter,);
    this.registerInclusionResolver('humanTwos', this.humanTwos.inclusionResolver);
  }
}
