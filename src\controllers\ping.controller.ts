import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  get,
  Request,
  response,
  ResponseObject,
  RestBindings,
} from '@loopback/rest';
import {SecurityBindings, UserProfile} from '@loopback/security';
/**
 * OpenAPI response for ping()
 */
const PING_RESPONSE: ResponseObject = {
  description: 'Ping Response',

};

/**
 * A simple controller to bounce back http requests
 */
@authenticate('cognito-jwt')
export class PingController {
  constructor(@inject(RestBindings.Http.REQUEST) private req: Request) { }

  // Map to `GET /ping`
  @get('/ping')
  @response(200, PING_RESPONSE)
  ping(@inject(SecurityBindings.USER)
  currentUserProfile: UserProfile): object {
    // Reply with a greeting, the current time, the url, and request headers
    return {
      greeting: `Hello from LoopBack ${process.env.COGNITO_REGION} `,
      date: new Date(),
      url: this.req.url,
      headers: currentUserProfile,
    };
  }
}
