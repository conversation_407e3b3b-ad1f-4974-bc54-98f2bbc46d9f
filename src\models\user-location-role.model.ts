import {Entity, model, property, belongsTo} from '@loopback/repository';
import {User} from './user.model';
import {LocationOne} from './location-one.model';
import {LocationTwo} from './location-two.model';
import {LocationThree} from './location-three.model';
import {LocationFour} from './location-four.model';

@model()
export class UserLocationRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  ehsRoles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  eptwRoles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  incidentRoles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  inspectionRoles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  plantRoles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  roles?: string[];
  @belongsTo(() => User)
  userId: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  constructor(data?: Partial<UserLocationRole>) {
    super(data);
  }
}

export interface UserLocationRoleRelations {
  // describe navigational properties here
}

export type UserLocationRoleWithRelations = UserLocationRole & UserLocationRoleRelations;
