import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  HttpErrors,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationTwo,
  LocationThree,
} from '../models';
import { LocationTwoRepository, UserRepository, UserLocationRoleRepository } from '../repositories';


import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';


@authenticate('jwt')
export class LocationTwoLocationThreeController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
    @repository(UserLocationRoleRepository) protected userLocationRoleRepository: UserLocationRoleRepository,
    @repository(LocationTwoRepository) protected locationTwoRepository: LocationTwoRepository,
  ) { }

  @get('/location-twos/{id}/location-threes', {
    responses: {
      '200': {
        description: 'Array of LocationTwo has many LocationThree',
        content: {
          'application/json': {
            schema: { type: 'array', items: getModelSchemaRef(LocationThree) },
          },
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<LocationThree>,
  ): Promise<LocationThree[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (!user) {
      throw new HttpErrors.NotFound(`User not found with this email: ${email}`);
    }

    // Check for 'tier1-all' access
    const hasTier1AllAccess = await this.userLocationRoleRepository.findOne({
      where: { userId: user.id, locationOneId: 'tier1-all' }
    });

    if (hasTier1AllAccess) {
      return this.locationTwoRepository.locationThrees(id).find(filter);
    }

    // Fetch the associated locationOneId for the provided locationTwoId
    const locationTwo = await this.locationTwoRepository.findById(id);
    const associatedLocationOneId = locationTwo.locationOneId;

    // Check if the user has 'tier2-all' access for the associated locationOneId
    const hasTier2AllAccessForAssociatedLocationOne = await this.userLocationRoleRepository.findOne({
      where: { userId: user.id, locationOneId: associatedLocationOneId, locationTwoId: 'tier2-all' }
    });

    if (hasTier2AllAccessForAssociatedLocationOne) {
      return this.locationTwoRepository.locationThrees(id).find(filter);
    }

    const userLocationRoles = await this.userLocationRoleRepository.find({ where: { userId: user.id, locationTwoId: id } });
    const locationThreeIds = userLocationRoles.map((userLocationRole) => userLocationRole.locationThreeId);
    const uniqueLocationThreeIds = Array.from(new Set(locationThreeIds));

    if (uniqueLocationThreeIds.includes('tier3-all')) {
      return this.locationTwoRepository.locationThrees(id).find(filter);
    } else {
      return this.locationTwoRepository.locationThrees(id).find({
        where: { id: { inq: uniqueLocationThreeIds } },
      });
    }
  }

  @post('/location-twos/{id}/location-threes', {
    responses: {
      '200': {
        description: 'LocationTwo model instance',
        content: { 'application/json': { schema: getModelSchemaRef(LocationThree) } },
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationTwo.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationThree, {
            title: 'NewLocationThreeInLocationTwo',
            exclude: ['id'],
            optional: ['locationTwoId']
          }),
        },
      },
    }) locationThree: Omit<LocationThree, 'id'>,
  ): Promise<LocationThree> {
    return this.locationTwoRepository.locationThrees(id).create(locationThree);
  }

  @patch('/location-twos/{id}/location-threes', {
    responses: {
      '200': {
        description: 'LocationTwo.LocationThree PATCH success count',
        content: { 'application/json': { schema: CountSchema } },
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationThree, { partial: true }),
        },
      },
    })
    locationThree: Partial<LocationThree>,
    @param.query.object('where', getWhereSchemaFor(LocationThree)) where?: Where<LocationThree>,
  ): Promise<Count> {
    return this.locationTwoRepository.locationThrees(id).patch(locationThree, where);
  }

  @del('/location-twos/{id}/location-threes', {
    responses: {
      '200': {
        description: 'LocationTwo.LocationThree DELETE success count',
        content: { 'application/json': { schema: CountSchema } },
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(LocationThree)) where?: Where<LocationThree>,
  ): Promise<Count> {
    return this.locationTwoRepository.locationThrees(id).delete(where);
  }
}
