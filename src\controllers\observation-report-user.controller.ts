import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  User,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportUserController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<User> {
    return this.observationReportRepository.reviewer(id);
  }
}
