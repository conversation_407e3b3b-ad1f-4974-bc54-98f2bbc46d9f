import {Entity, model, property, hasMany} from '@loopback/repository';
import {LocationTwo} from './location-two.model';
import {ObservationReport} from './observation-report.model';
import {UserLocationRole} from './user-location-role.model';
import {PermitReport} from './permit-report.model';

@model()
export class LocationOne extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @hasMany(() => LocationTwo)
  locationTwos: LocationTwo[];

  @hasMany(() => ObservationReport)
  observationReports: ObservationReport[];

  @hasMany(() => UserLocationRole)
  userLocationRoles: UserLocationRole[];

  @hasMany(() => PermitReport)
  permitReports: PermitReport[];

  constructor(data?: Partial<LocationOne>) {
    super(data);
  }
}

export interface LocationOneRelations {
  // describe navigational properties here
}

export type LocationOneWithRelations = LocationOne & LocationOneRelations;
