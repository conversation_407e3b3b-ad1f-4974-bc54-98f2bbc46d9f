import {Entity, model, property, hasMany} from '@loopback/repository';
import {HazardType} from './hazard-type.model';

@model()
export class HazardCategory extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @hasMany(() => HazardType)
  hazardTypes: HazardType[];

  constructor(data?: Partial<HazardCategory>) {
    super(data);
  }
}

export interface HazardCategoryRelations {
  // describe navigational properties here
}

export type HazardCategoryWithRelations = HazardCategory & HazardCategoryRelations;
