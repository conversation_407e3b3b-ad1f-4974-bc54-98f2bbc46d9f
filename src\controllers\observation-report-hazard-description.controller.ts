import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  HazardDescription,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportHazardDescriptionController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/hazard-description', {
    responses: {
      '200': {
        description: 'HazardDescription belonging to ObservationReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(HazardDescription),
          },
        },
      },
    },
  })
  async getHazardDescription(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<HazardDescription> {
    return this.observationReportRepository.hazardDescription(id);
  }
}
