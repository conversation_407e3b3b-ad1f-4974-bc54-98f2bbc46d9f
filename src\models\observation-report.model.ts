import { Entity, model, property, belongsTo, hasMany } from '@loopback/repository';
import { WorkActivity } from './work-activity.model';
import { GhsOne } from './ghs-one.model';
import { GhsTwo } from './ghs-two.model';
import { LocationOne } from './location-one.model';
import { LocationTwo } from './location-two.model';
import { LocationThree } from './location-three.model';
import { LocationFour } from './location-four.model';
import { LocationFive } from './location-five.model';
import { LocationSix } from './location-six.model';
import { Action } from './action.model';
import { User } from './user.model';
import {HazardCategory} from './hazard-category.model';
import {HazardType} from './hazard-type.model';
import {HazardDescription} from './hazard-description.model';

@model()
export class ObservationReport extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  type?: string;

  @property({
    type: 'string',
  })
  generalCondition?: string;

  @property({
    type: 'string',
  })
  describeSafekObservation?: string;

  @property({
    type: 'string',
  })
  conditionAct?: string;

  @property({
    type: 'string',
  })
  describeAtRiskObservation?: string;

  @property({
    type: 'string',
  })
  describeActionTaken?: string;


  @property({
    type: 'string',
  })
  category?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  rectifiedStatus?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  evidence?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'array',
    itemType: 'string',

  })
  evidences?: string[];

  @property({
    type: 'string',
  })
  remarks?: string;

  @property({
    type: 'string',
  })
  actionTaken?: string;

  @property({
    type: 'string',
  })
  actionToBeTaken?: string;

  @property({
    type: 'string',
  })
  dueDate?: string;

  @property({
    type: 'string',
  })
  qrRole?: string;

  @property({
    type: 'boolean',
  })
  isQR?: boolean;

  @property({
    type: 'string',
  })
  status?: string;
  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  qrLink?: string;

  @property({
    type: 'boolean',
  })
  active?: boolean;

  @belongsTo(() => WorkActivity)
  workActivityId: string;
  @belongsTo(() => GhsOne)
  ghsOneId: string;

  @belongsTo(() => GhsTwo)
  ghsTwoId: string;
  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  @hasMany(() => Action, {keyTo: 'objectId'})
  actions: Action[];

  @belongsTo(() => User)
  submittedId: string;

  @belongsTo(() => User)
  actionOwnerId: string;

  @belongsTo(() => User)
  reviewerId: string;

  @belongsTo(() => User)
  qroAssigneeId: string;

  locationOne?: LocationOne;
  locationTwo?: LocationTwo;
  locationThree?: LocationThree;

  locationFour?: LocationFour;
  locationFive?: LocationFive;
  locationSix?: LocationSix;
  
  ghsOne?: GhsOne;

  submitted?: User;

  @belongsTo(() => HazardCategory)
  hazardCategoryId: string;

  @belongsTo(() => HazardType)
  hazardTypeId: string;

  @belongsTo(() => HazardDescription)
  hazardDescriptionId: string;

  hazardCategory: HazardCategory;
  hazardType: HazardType;
  hazardDescription: HazardDescription;

  constructor(data?: Partial<ObservationReport>) {
    super(data);
  }
}

export interface ObservationReportRelations {
  // describe navigational properties here
}

export type ObservationReportWithRelations = ObservationReport & ObservationReportRelations;
