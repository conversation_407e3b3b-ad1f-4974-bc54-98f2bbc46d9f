import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {EhsRole} from '../models';
import {EhsRoleRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class EhsRoleController {
  constructor(
    @repository(EhsRoleRepository)
    public ehsRoleRepository : EhsRoleRepository,
  ) {}

  @post('/ehs-roles')
  @response(200, {
    description: 'EhsRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(EhsRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EhsRole, {
            title: 'NewEhsRole',
            exclude: ['id'],
          }),
        },
      },
    })
    ehsRole: Omit<EhsRole, 'id'>,
  ): Promise<EhsRole> {
    return this.ehsRoleRepository.create(ehsRole);
  }

  @get('/ehs-roles/count')
  @response(200, {
    description: 'EhsRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(EhsRole) where?: Where<EhsRole>,
  ): Promise<Count> {
    return this.ehsRoleRepository.count(where);
  }

  @get('/ehs-roles')
  @response(200, {
    description: 'Array of EhsRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EhsRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(EhsRole) filter?: Filter<EhsRole>,
  ): Promise<EhsRole[]> {
    return this.ehsRoleRepository.find(filter);
  }

  @patch('/ehs-roles')
  @response(200, {
    description: 'EhsRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EhsRole, {partial: true}),
        },
      },
    })
    ehsRole: EhsRole,
    @param.where(EhsRole) where?: Where<EhsRole>,
  ): Promise<Count> {
    return this.ehsRoleRepository.updateAll(ehsRole, where);
  }

  @get('/ehs-roles/{id}')
  @response(200, {
    description: 'EhsRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(EhsRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(EhsRole, {exclude: 'where'}) filter?: FilterExcludingWhere<EhsRole>
  ): Promise<EhsRole> {
    return this.ehsRoleRepository.findById(id, filter);
  }

  @patch('/ehs-roles/{id}')
  @response(204, {
    description: 'EhsRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EhsRole, {partial: true}),
        },
      },
    })
    ehsRole: EhsRole,
  ): Promise<void> {
    await this.ehsRoleRepository.updateById(id, ehsRole);
  }

  @put('/ehs-roles/{id}')
  @response(204, {
    description: 'EhsRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() ehsRole: EhsRole,
  ): Promise<void> {
    await this.ehsRoleRepository.replaceById(id, ehsRole);
  }

  @del('/ehs-roles/{id}')
  @response(204, {
    description: 'EhsRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.ehsRoleRepository.deleteById(id);
  }
}
