import {Entity, model, property, belongsTo} from '@loopback/repository';
import {User} from './user.model';

@model()
export class ServiceNow extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  desc?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @belongsTo(() => User)
  userId: string;

  constructor(data?: Partial<ServiceNow>) {
    super(data);
  }
}

export interface ServiceNowRelations {
  // describe navigational properties here
}

export type ServiceNowWithRelations = ServiceNow & ServiceNowRelations;
