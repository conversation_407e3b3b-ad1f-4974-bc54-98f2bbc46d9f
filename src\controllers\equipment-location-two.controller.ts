import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Equipment,
  LocationTwo,
} from '../models';
import {EquipmentRepository} from '../repositories';

export class EquipmentLocationTwoController {
  constructor(
    @repository(EquipmentRepository)
    public equipmentRepository: EquipmentRepository,
  ) { }

  @get('/equipment/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to Equipment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationTwo)},
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof Equipment.prototype.id,
  ): Promise<LocationTwo> {
    return this.equipmentRepository.locationTwo(id);
  }
}
