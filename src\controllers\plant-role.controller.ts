import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {PlantRole} from '../models';
import {PlantRoleRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class PlantRoleController {
  constructor(
    @repository(PlantRoleRepository)
    public plantRoleRepository : PlantRoleRepository,
  ) {}

  @post('/plant-roles')
  @response(200, {
    description: 'PlantRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(PlantRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PlantRole, {
            title: 'NewPlantRole',
            exclude: ['id'],
          }),
        },
      },
    })
    plantRole: Omit<PlantRole, 'id'>,
  ): Promise<PlantRole> {
    return this.plantRoleRepository.create(plantRole);
  }

  @get('/plant-roles/count')
  @response(200, {
    description: 'PlantRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(PlantRole) where?: Where<PlantRole>,
  ): Promise<Count> {
    return this.plantRoleRepository.count(where);
  }

  @get('/plant-roles')
  @response(200, {
    description: 'Array of PlantRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(PlantRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(PlantRole) filter?: Filter<PlantRole>,
  ): Promise<PlantRole[]> {
    return this.plantRoleRepository.find(filter);
  }

  @patch('/plant-roles')
  @response(200, {
    description: 'PlantRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PlantRole, {partial: true}),
        },
      },
    })
    plantRole: PlantRole,
    @param.where(PlantRole) where?: Where<PlantRole>,
  ): Promise<Count> {
    return this.plantRoleRepository.updateAll(plantRole, where);
  }

  @get('/plant-roles/{id}')
  @response(200, {
    description: 'PlantRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(PlantRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(PlantRole, {exclude: 'where'}) filter?: FilterExcludingWhere<PlantRole>
  ): Promise<PlantRole> {
    return this.plantRoleRepository.findById(id, filter);
  }

  @patch('/plant-roles/{id}')
  @response(204, {
    description: 'PlantRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PlantRole, {partial: true}),
        },
      },
    })
    plantRole: PlantRole,
  ): Promise<void> {
    await this.plantRoleRepository.updateById(id, plantRole);
  }

  @put('/plant-roles/{id}')
  @response(204, {
    description: 'PlantRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() plantRole: PlantRole,
  ): Promise<void> {
    await this.plantRoleRepository.replaceById(id, plantRole);
  }

  @del('/plant-roles/{id}')
  @response(204, {
    description: 'PlantRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.plantRoleRepository.deleteById(id);
  }
}
