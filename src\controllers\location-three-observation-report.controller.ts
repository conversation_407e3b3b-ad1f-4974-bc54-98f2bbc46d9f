import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationThree,
  ObservationReport,
} from '../models';
import {LocationThreeRepository} from '../repositories';

export class LocationThreeObservationReportController {
  constructor(
    @repository(LocationThreeRepository) protected locationThreeRepository: LocationThreeRepository,
  ) { }

  @get('/location-threes/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'Array of LocationThree has many ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ObservationReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.locationThreeRepository.observationReports(id).find(filter);
  }

  @post('/location-threes/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationThree model instance',
        content: {'application/json': {schema: getModelSchemaRef(ObservationReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationThree.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReportInLocationThree',
            exclude: ['id'],
            optional: ['locationThreeId']
          }),
        },
      },
    }) observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    return this.locationThreeRepository.observationReports(id).create(observationReport);
  }

  @patch('/location-threes/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationThree.ObservationReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: Partial<ObservationReport>,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationThreeRepository.observationReports(id).patch(observationReport, where);
  }

  @del('/location-threes/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationThree.ObservationReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationThreeRepository.observationReports(id).delete(where);
  }
}
