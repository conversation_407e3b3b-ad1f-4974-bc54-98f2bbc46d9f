import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportData,
  LocationOne,
} from '../models';
import {ReportDataRepository} from '../repositories';

export class ReportDataLocationOneController {
  constructor(
    @repository(ReportDataRepository)
    public reportDataRepository: ReportDataRepository,
  ) { }

  @get('/report-data/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to ReportData',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationOne)},
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof ReportData.prototype.id,
  ): Promise<LocationOne> {
    return this.reportDataRepository.locationOne(id);
  }
}
