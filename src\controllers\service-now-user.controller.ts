import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ServiceNow,
  User,
} from '../models';
import {ServiceNowRepository} from '../repositories';

export class ServiceNowUserController {
  constructor(
    @repository(ServiceNowRepository)
    public serviceNowRepository: ServiceNowRepository,
  ) { }

  @get('/service-nows/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to ServiceNow',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof ServiceNow.prototype.id,
  ): Promise<User> {
    return this.serviceNowRepository.user(id);
  }
}
