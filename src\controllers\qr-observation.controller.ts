import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  model,
  property,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { Action, ObservationReport, User } from '../models';
import { ObservationReportRepository, UserLocationRoleRepository, QrLinkRepository, UserRepository, ActionRepository, LocationOneRepository, LocationTwoRepository, LocationThreeRepository, LocationFourRepository } from '../repositories';
import moment from 'moment';
import { SqsService } from '../services/sqs-service.service';
import { inject } from '@loopback/core';
import { v4 as uuidv4 } from 'uuid';

@model()
export class RemarksAction extends Action {
  @property({
    type: 'string',

  })
  remarks: string;
}

function formatDueDate(isoDate: string) {
  return moment(isoDate).format('Do MMM YYYY');
}


export class QrObservationController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(QrLinkRepository)
    public qrLinkRepository: QrLinkRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/qr-observation-reports')
  @response(200, {
    description: 'ObservationReport model instance',
    content: { 'application/json': { schema: getModelSchemaRef(ObservationReport) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReport',
            exclude: ['id'],
          }),
        },
      },
    })
    observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {

    const linkCode = uuidv4();
    observationReport.qrLink = linkCode;

    const count = await this.observationReportRepository.count();
    observationReport.description = observationReport.describeSafekObservation ? observationReport.describeSafekObservation : observationReport.describeAtRiskObservation;
    observationReport.isQR = true;
    observationReport.maskId = `OBS-${moment().format('YYMMDD')}-${String(count.count + 1).padStart(4, '0')} (QR)`;
    const obs = await this.observationReportRepository.create(observationReport);
    const obsData = await this.observationReportRepository.findById(obs.id, { include: [{ relation: "locationOne" }, { relation: "locationTwo" }, { relation: "locationThree" }, { relation: "locationFour" }, { relation: "locationFive" }, { relation: "locationSix" }] });
    if (observationReport.qrRole !== 'Main Contractor' && observationReport.qrRole !== 'Sub-contractor') {
      const qrAssignee = await this.findUsers(observationReport.locationOneId, observationReport.locationTwoId, observationReport.locationThreeId, observationReport.locationFourId);
      observationReport.isQR = true;

      // If qrAssignee array has users, send email to each user
      if (qrAssignee && qrAssignee.length > 0) {
        qrAssignee.forEach(user => {
          observationReport.qroAssigneeId = user.id;

          const mailSubject = `Review Observation - ${obsData.maskId}`;
          const mailBody = `<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Observation Report Email</title>
      </head>
      <body>
         
          <p>An EHS Observation has been reported for your follow up. </p>
          <p><strong>ID:</strong>  ${obsData.maskId}</p>
          <p><strong>Location:</strong> ${obsData?.locationOne?.name} > ${obsData?.locationTwo?.name} > ${obsData?.locationThree?.name} > ${obsData?.locationFour?.name} > ${obsData?.locationFive?.name} > ${obsData?.locationSix?.name}</p>
          <p><strong>Description:</strong> ${obsData.description}</p>
          <p>Please click the link to Review the Observation. <a href="http://qr.stt-gdc.acuizen.com/publicObs/edit/${obsData.id}/${linkCode}">Click Here</a></p>
      </body>
      </html>`;

          // Send message to each user
          this.sqsService.sendMessage(user, mailSubject, mailBody);
        });
      } else {
        // If no users found, throw an error
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    } else {
      if (observationReport.type === 'At-Risk' && observationReport.rectifiedStatus === 'No') {

        const actionItem = {
          application: "Observation",
          actionType: "action_owner",
          comments: observationReport.actionTaken,
          description: observationReport.describeAtRiskObservation,
          dueDate: observationReport.dueDate,
          actionToBeTaken: observationReport.actionToBeTaken,
          status: "open",
          createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
          objectId: obsData.id,
          submittedById: '',
          assignedToId: observationReport.actionOwnerId
        }
        //send action card mail
        const actionOwner = await this.userRepository.findById(observationReport.actionOwnerId);
        const actionData = await this.actionRepository.create(actionItem)
        const mailSubject = `Take Action - ${obsData.maskId} | ${obsData?.locationThree?.name} > ${obsData?.locationFour?.name}`;
        const mailBody = `<!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Observation Report Email</title>
          </head>
          <body>
           
        
          <p>An Action has been created from an EHS observation for your follow up. </p>
          <p><strong>ID:</strong>  ${obsData.maskId}</p>
          <p><strong>Location:</strong> ${obsData?.locationOne?.name} > ${obsData?.locationTwo?.name} > ${obsData?.locationThree?.name} > ${obsData?.locationFour?.name} > ${obsData?.locationFive?.name} > ${obsData?.locationSix?.name}</p>
          <p><strong>Description:</strong> ${obsData.description}</p>
          <p><strong>Action to be Taken:</strong> ${observationReport.actionToBeTaken}</p>
          <p><strong>Due Date:</strong> ${formatDueDate(observationReport?.dueDate ?? '')}</p>
          <p>Please click the link to Take Action. <a href="http://qr.stt-gdc.acuizen.com/publicObs/action/${actionData.id}/${linkCode}">Click Here</a></p>
          </body>
          </html>`;
        if (actionOwner) { await this.sqsService.sendMessage(actionOwner, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
        // await this.actionRepository.create(actionItem)
        observationReport.status = 'At Risk - Actions Assigned';
      }
      else {
        if (observationReport.type !== 'Safe')
          observationReport.status = 'At Risk - Closed';
      }
    }
    await this.observationReportRepository.updateById(obsData.id, { status: observationReport.status })
    return obsData;
  }

  @get('/qr-observation-reports/count')
  @response(200, {
    description: 'ObservationReport model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(ObservationReport) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.observationReportRepository.count(where);
  }

  @get('/qr-observation-reports')
  @response(200, {
    description: 'Array of ObservationReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ObservationReport, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(ObservationReport) filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.observationReportRepository.find(filter);
  }

  @get('/qr-observation-reports-get-actions')
  @response(200, {
    description: 'Array of ObservationReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ObservationReport, { includeRelations: true }),
        },
      },
    },
  })
  async findActions(
    @param.filter(ObservationReport) filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.observationReportRepository.find(filter);
  }


  @patch('/qr-observation-reports')
  @response(200, {
    description: 'ObservationReport PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, { partial: true }),
        },
      },
    })
    observationReport: ObservationReport,
    @param.where(ObservationReport) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.observationReportRepository.updateAll(observationReport, where);
  }

  @get('/qr-observation-reports/{id}')
  @response(200, {
    description: 'ObservationReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ObservationReport, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ObservationReport, { exclude: 'where' }) filter?: FilterExcludingWhere<ObservationReport>
  ): Promise<ObservationReport> {
    return this.observationReportRepository.findById(id, filter);
  }

  @patch('/qr-observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, { partial: true }),
        },
      },
    })
    observationReport: ObservationReport,
  ): Promise<void> {
    const linkCode = uuidv4();
    observationReport.qrLink = linkCode;
    const obsData = await this.observationReportRepository.findById(id, { include: [{ relation: "locationOne" }, { relation: "locationTwo" }, { relation: "locationThree" }, { relation: "locationFour" }, {relation: 'locationFive'}, {relation: 'locationSix'}] });
    if (observationReport.type === 'At-Risk' && observationReport.rectifiedStatus === 'No') {

      const actionItem = {
        application: "Observation",
        actionType: "action_owner",
        comments: observationReport.actionTaken,
        description: observationReport.describeAtRiskObservation,
        dueDate: observationReport.dueDate,
        actionToBeTaken: observationReport.actionToBeTaken,
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: obsData.id,
        submittedById: '',
        assignedToId: observationReport.actionOwnerId
      }
      //send action card mail
      const actionOwner = await this.userRepository.findById(observationReport.actionOwnerId)
      const actionData = await this.actionRepository.create(actionItem)
      const mailSubject = `Take Action - ${obsData.maskId} | ${obsData?.locationThree?.name} > ${obsData?.locationFour?.name}`;
      const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Observation Report Email</title>
        </head>
        <body>
         
      
             <p>An Action has been created from an EHS observation for your follow up. </p>

             <p><strong>ID:</strong>  ${obsData.maskId}</p>
          <p><strong>Location:</strong> ${obsData?.locationOne?.name} > ${obsData?.locationTwo?.name} > ${obsData?.locationThree?.name} > ${obsData?.locationFour?.name} > ${obsData?.locationFive?.name} > ${obsData?.locationSix?.name}</p>
          <p><strong>Description:</strong> ${obsData.description}</p>
          <p><strong>Action to be Taken:</strong> ${observationReport.actionToBeTaken}</p>
          <p><strong>Due Date:</strong> ${formatDueDate(observationReport?.dueDate ?? '')}</p>
        <p>Please click the link to Take Action. <a href="http://qr.stt-gdc.acuizen.com/publicObs/action/${actionData.id}/${linkCode}">Click Here</a></p>
        </body>
        </html>`;
      if (actionOwner) { await this.sqsService.sendMessage(actionOwner, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }


      observationReport.status = 'At Risk - Actions Assigned';
    }
    else {
      if (observationReport.type !== 'Safe')
        observationReport.status = 'At Risk - Closed';
    }
    await this.observationReportRepository.updateById(id, observationReport);
  }

  @put('/qr-observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() observationReport: ObservationReport,
  ): Promise<void> {
    await this.observationReportRepository.replaceById(id, observationReport);
  }

  @del('/qr-observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.observationReportRepository.deleteById(id);
  }

  async findUsers(locationOneId: string, locationTwoId: string, locationThreeId: string, locationFourId: string,): Promise<User[]> {


    const roleId = "673fe74d9e33816c979466e8";
    const whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [locationOneId] },
          locationTwoId: { inq: [locationTwoId] },
          locationThreeId: { inq: [locationThreeId] },
          locationFourId: { inq: [locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [locationOneId] },
          locationTwoId: { inq: [locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [locationOneId] },
          locationTwoId: { inq: [locationTwoId] },
          locationThreeId: { inq: [locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @get('/qr-actions/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async getActionQrById(

    @param.path.string('id') id: string,

  ): Promise<any> {
    const actionData = await this.actionRepository.findById(id);
    const obsData = await this.observationReportRepository.findById(actionData.objectId, { include: [{ relation: "hazardCategory" }, { relation: "hazardDescription" }, { relation: "locationOne" }, { relation: "locationTwo" }, { relation: "locationThree" }, { relation: "locationFour" }, { relation: "locationFive" }, { relation: "locationSix" }] });

    return { action: actionData, obs: obsData }
  }

  @patch('/qr-actions/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateActionQrById(

    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RemarksAction, { partial: true }),
        },
      },
    })
    action: RemarksAction,
  ): Promise<void> {
    const linkCode = uuidv4();

    action.status = 'submitted'
    if (action.actionType === 'reviewer') {
      delete action.actionType
      await this.observationReportRepository.updateById(action.objectId, { qrLink: linkCode, remarks: action?.remarks || '', status: 'At Risk - Actions to be Verified', evidences: action.uploads, actionTaken: action.actionTaken, reviewerId: action.assignedToId });


      const actionItem = {
        application: "Observation",
        actionType: "reviewer",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: action.objectId,
        submittedById: '',
        assignedToId: action.assignedToId
      }


      if (action.assignedToId && action.objectId) {

        const observationDetails = await this.observationReportRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })
        const reviewerDetails = await this.userRepository.findById(action.assignedToId)
        actionItem.createdDate = moment().utcOffset("+08:00").format('YYYY-MM-DDTHH:mm:ss.SSSZ');
        const actionData = await this.actionRepository.create(actionItem)

        const locationOneName = (observationDetails as any).locationOne?.name;
        const locationTwoName = (observationDetails as any).locationTwo?.name;
        const locationThreeName = (observationDetails as any).locationThree?.name;
        const locationFourName = (observationDetails as any).locationFour?.name;
        const locationFiveName = (observationDetails as any).locationFive?.name;
        const locationSixName = (observationDetails as any).locationSix?.name;

        const mailSubject = `Review Action - ${observationDetails.maskId} : ${locationFiveName}, ${locationSixName}`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Observation Report Email</title>
        </head>
        <body>
          
            <p>Action is taken on EHS observation.</p>
         
            
            <p><strong>ID:</strong>  ${observationDetails.maskId}</p>
           <p><strong>Location: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName} &gt; ${locationFiveName} &gt; ${locationSixName}</p>
          
            <p><strong>Description:</strong> ${observationDetails.description}</p>
           ${action.comments ? ` <p><strong>Comments from the reviewer:</strong> ${action.comments}` : ''}</p> 
            <p><strong>Action to be Taken:</strong> ${action.actionToBeTaken}</p>
           <p><strong>Action Taken:</strong> ${action.actionTaken}</p>
             <p><strong>Due Date:</strong> ${formatDueDate(action.dueDate ?? '')}</p>
            
           
            <p>Please click the link to Review Action <a href="http://qr.stt-gdc.acuizen.com/publicObs/action-verify/${actionData.id}/${linkCode}">Click Here</a> </p>
        </body>
        </html>`;


        if (reviewerDetails) { await this.sqsService.sendMessage(reviewerDetails, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }


    }

    if (action.actionType === 'approve') {

      if (action.objectId) {
        await this.observationReportRepository.updateById(action.objectId, { qrLink: linkCode, remarks: action?.remarks || '', status: 'At Risk - Closed', actionTaken: action.actionTaken });
        const observationDetails = await this.observationReportRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })

        if (observationDetails.actionOwnerId) {


          const locationOneName = (observationDetails as any).locationOne?.name;
          const locationTwoName = (observationDetails as any).locationTwo?.name;
          const locationThreeName = (observationDetails as any).locationThree?.name;
          const locationFourName = (observationDetails as any).locationFour?.name;
          const locationFiveName = (observationDetails as any).locationFive?.name;
          const locationSixName = (observationDetails as any).locationSix?.name;


          const actionOwnerUser = await this.userRepository.findById(observationDetails.actionOwnerId);
          const reviewerUser = await this.userRepository.findById(observationDetails.reviewerId);

          const mailSubject = ` Action Approved - ${observationDetails.maskId}: ${locationFiveName}, ${locationSixName}`;
          const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Observation Report Email</title>
        </head>
        <body>
            <p>The action from this observation is approved. This observation is closed.</p>
            <p><strong>ID:</strong>  ${observationDetails.maskId}</p>
            <p><strong>Description:</strong> ${observationDetails.description}</p>
            <p><strong>Name of Reviewer: </strong> ${reviewerUser?.firstName}</p>
            <p><strong>Location: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName} &gt; ${locationFiveName} &gt; ${locationSixName}</p>
           <p><strong>Action to be Taken: </strong> ${action.actionToBeTaken}</p>
            <p><strong>Action taken by Assignee: </strong> ${action.actionTaken}</p>
            <p><strong>Comments from Approver: </strong> ${action.remarks}</p>
        </body>
        </html>`;

          if (actionOwnerUser) { await this.sqsService.sendMessage(actionOwnerUser, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }


        }

      }

    }

    if (action.actionType === 'reject') {
      if (action.objectId) {
        const obsData = await this.observationReportRepository.findById(action.objectId)
        await this.observationReportRepository.updateById(action.objectId, { qrLink: linkCode, remarks: action?.remarks || '', status: 'At Risk - Actions Re-Assigned', actionOwnerId: obsData.actionOwnerId });
        const actionItem = {
          application: "Observation",
          actionType: "action_owner",
          comments: action.comments,
          actionTaken: action.actionTaken,
          actionToBeTaken: action.actionToBeTaken,
          description: action.description,
          dueDate: action.dueDate,
          status: "returned",
          createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
          uploads: action.uploads,
          objectId: action.objectId,
          submittedById: '',
          assignedToId: obsData.actionOwnerId
        }


        const observationDetails = await this.observationReportRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })
        const locationOneName = (observationDetails as any).locationOne?.name;
        const locationTwoName = (observationDetails as any).locationTwo?.name;
        const locationThreeName = (observationDetails as any).locationThree?.name;
        const locationFourName = (observationDetails as any).locationFour?.name;
        const locationFiveName = (observationDetails as any).locationFive?.name;
        const locationSixName = (observationDetails as any).locationSix?.name;

        const reviewerDetails = await this.userRepository.findById(obsData.reviewerId)

        const actionOwnerDetails = await this.userRepository.findById(obsData.actionOwnerId)
        actionItem.createdDate = moment().utcOffset("+08:00").format('YYYY-MM-DDTHH:mm:ss.SSSZ');
        const actionData = await this.actionRepository.create(actionItem)

        const mailSubject = ` Action Returned.  Resubmit Action - ${observationDetails.maskId}. ${locationFiveName}, ${locationSixName}`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Observation Report Email</title>
        </head>
        <body>
           <p>An action from EHS observation has been returned by the action reviewer. </p>
           
            <p><strong>ID:</strong>  ${observationDetails.maskId}</p>
            <p><strong>Location: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName} &gt; ${locationFiveName} &gt; ${locationSixName}</p>
            <p><strong>Description: </strong> ${observationDetails.description}</p>
            <p><strong>Name of the Reviewer: </strong> ${reviewerDetails.firstName}</p>
            <p><strong>Action to be Taken: </strong> ${action.actionToBeTaken}</p>
            <p><strong>Action Taken: </strong> ${action.actionTaken}</p>
            <p><strong>Comments from the reviewer: </strong> ${action.comments}</p>
            <p><strong>Due date:</strong> ${formatDueDate(action.dueDate ?? '')}</p>
           <p>Please click the link to resubmit Action <a href="http://qr.stt-gdc.acuizen.com/publicObs/action/${actionData.id}/${linkCode}">Click Here</a> </p>
        </body>
        </html>`;
        if (actionOwnerDetails) { await this.sqsService.sendMessage(actionOwnerDetails, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }


      }
    }
    delete action.assignedToId
    action.createdDate = moment().utcOffset("+08:00").format('YYYY-MM-DDTHH:mm:ss.SSSZ');
    await this.actionRepository.updateById(id, action);
  }


  async getLocationDetails(
    locationFourId: string,
  ): Promise<any> {
    let locationFour, locationThree, locationTwo, locationOne;

    try {
      locationFour = await this.locationFourRepository.findById(locationFourId);
    } catch (error) {
      locationFour = { id: '', name: '', locationThreeId: '' };
    }

    try {
      locationThree = await this.locationThreeRepository.findById(locationFour.locationThreeId);
    } catch (error) {
      locationThree = { id: '', name: '', locationTwoId: '' };
    }

    try {
      locationTwo = await this.locationTwoRepository.findById(locationThree.locationTwoId);
    } catch (error) {
      locationTwo = { id: '', name: '', locationOneId: '' };
    }

    try {
      locationOne = await this.locationOneRepository.findById(locationTwo.locationOneId);
    } catch (error) {
      locationOne = { id: '', name: '' };
    }

    return {
      locationOne: {
        id: locationOne.id,
        name: locationOne.name,
      },
      locationTwo: {
        id: locationTwo.id,
        name: locationTwo.name,
        locationOneId: locationTwo.locationOneId,
      },
      locationThree: {
        id: locationThree.id,
        name: locationThree.name,
        locationTwoId: locationThree.locationTwoId,
      },
      locationFour: {
        id: locationFour.id,
        name: locationFour.name,
        locationThreeId: locationFour.locationThreeId,
      },
    };
  }


}
