import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  IncidentCircumstanceDescription,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentIncidentCircumstanceDescriptionController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/incident-circumstance-description', {
    responses: {
      '200': {
        description: 'IncidentCircumstanceDescription belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentCircumstanceDescription)},
          },
        },
      },
    },
  })
  async getIncidentCircumstanceDescription(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<IncidentCircumstanceDescription> {
    return this.reportIncidentRepository.incidentCircumstanceDescription(id);
  }
}
