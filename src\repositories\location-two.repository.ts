import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationTwo, LocationTwoRelations, LocationThree, ObservationReport, UserLocationRole, PermitReport} from '../models';
import {LocationThreeRepository} from './location-three.repository';
import {ObservationReportRepository} from './observation-report.repository';
import {UserLocationRoleRepository} from './user-location-role.repository';
import {PermitReportRepository} from './permit-report.repository';

export class LocationTwoRepository extends DefaultCrudRepository<
  LocationTwo,
  typeof LocationTwo.prototype.id,
  LocationTwoRelations
> {

  public readonly locationThrees: HasManyRepositoryFactory<LocationThree, typeof LocationTwo.prototype.id>;

  public readonly observationReports: HasManyRepositoryFactory<ObservationReport, typeof LocationTwo.prototype.id>;

  public readonly userLocationRoles: HasManyRepositoryFactory<UserLocationRole, typeof LocationTwo.prototype.id>;

  public readonly permitReports: HasManyRepositoryFactory<PermitReport, typeof LocationTwo.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('ObservationReportRepository') protected observationReportRepositoryGetter: Getter<ObservationReportRepository>, @repository.getter('UserLocationRoleRepository') protected userLocationRoleRepositoryGetter: Getter<UserLocationRoleRepository>, @repository.getter('PermitReportRepository') protected permitReportRepositoryGetter: Getter<PermitReportRepository>,
  ) {
    super(LocationTwo, dataSource);
    this.permitReports = this.createHasManyRepositoryFactoryFor('permitReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('permitReports', this.permitReports.inclusionResolver);
    this.userLocationRoles = this.createHasManyRepositoryFactoryFor('userLocationRoles', userLocationRoleRepositoryGetter,);
    this.registerInclusionResolver('userLocationRoles', this.userLocationRoles.inclusionResolver);
    this.observationReports = this.createHasManyRepositoryFactoryFor('observationReports', observationReportRepositoryGetter,);
    this.registerInclusionResolver('observationReports', this.observationReports.inclusionResolver);
    this.locationThrees = this.createHasManyRepositoryFactoryFor('locationThrees', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThrees', this.locationThrees.inclusionResolver);
  }
}
