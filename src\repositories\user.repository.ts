// Copyright IBM Corp. 2019,2020. All Rights Reserved.
// Node module: loopback4-example-shopping
// This file is licensed under the MIT License.
// License text available at https://opensource.org/licenses/MIT

import {Getter, inject} from '@loopback/core';
import {
  DefaultCrudRepository,
  HasManyRepositoryFactory,
  HasOneRepositoryFactory,
  juggler,
  repository, BelongsToAccessor} from '@loopback/repository';
import { User, UserCredentials, EhsRole, Action, UserLocation, UserLocationRole, ReportIncident, TowerCrane, PermitReport, ServiceNow} from '../models';

import {UserCredentialsRepository} from './user-credentials.repository';
import {EhsRoleRepository} from './ehs-role.repository';
import {UserLocationRepository} from './user-location.repository';
import {UserLocationRoleRepository} from './user-location-role.repository';
import {ReportIncidentRepository} from './report-incident.repository';
import {TowerCraneRepository} from './tower-crane.repository';
import {PermitReportRepository} from './permit-report.repository';
import {ServiceNowRepository} from './service-now.repository';

// import {ActionRepository} from './action.repository';

export type Credentials = {
  email: string;
  password: string;
};

export class UserRepository extends DefaultCrudRepository<
  User,
  typeof User.prototype.id
> {
 

  public readonly userCredentials: HasOneRepositoryFactory<
    UserCredentials,
    typeof User.prototype.id
  >;

  public readonly ehsRole: BelongsToAccessor<EhsRole, typeof User.prototype.id>;

  public readonly actions: HasManyRepositoryFactory<Action, typeof User.prototype.id>;

  public readonly UserAssigned: HasManyRepositoryFactory<Action, typeof User.prototype.id>;

  public readonly userLocation: HasOneRepositoryFactory<UserLocation, typeof User.prototype.id>;

  public readonly userLocationRoles: HasManyRepositoryFactory<UserLocationRole, typeof User.prototype.id>;

  public readonly reportIncidents: HasManyRepositoryFactory<ReportIncident, typeof User.prototype.id>;

  public readonly towerCranes: HasManyRepositoryFactory<TowerCrane, typeof User.prototype.id>;

  public readonly applicantReports: HasManyRepositoryFactory<PermitReport, typeof User.prototype.id>;

  public readonly assessorReports: HasManyRepositoryFactory<PermitReport, typeof User.prototype.id>;

  public readonly approverReports: HasManyRepositoryFactory<PermitReport, typeof User.prototype.id>;

  public readonly dscoApproverReports: HasManyRepositoryFactory<PermitReport, typeof User.prototype.id>;

  public readonly serviceNows: HasManyRepositoryFactory<ServiceNow, typeof User.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: juggler.DataSource,
    @repository.getter('UserCredentialsRepository')
    protected userCredentialsRepositoryGetter: Getter<UserCredentialsRepository>, @repository.getter('EhsRoleRepository') protected ehsRoleRepositoryGetter: Getter<EhsRoleRepository>, @repository.getter('UserLocationRepository') protected userLocationRepositoryGetter: Getter<UserLocationRepository>, @repository.getter('UserLocationRoleRepository') protected userLocationRoleRepositoryGetter: Getter<UserLocationRoleRepository>, @repository.getter('ReportIncidentRepository') protected reportIncidentRepositoryGetter: Getter<ReportIncidentRepository>, @repository.getter('TowerCraneRepository') protected towerCraneRepositoryGetter: Getter<TowerCraneRepository>, @repository.getter('PermitReportRepository') protected permitReportRepositoryGetter: Getter<PermitReportRepository>, @repository.getter('ServiceNowRepository') protected serviceNowRepositoryGetter: Getter<ServiceNowRepository>,
  ) {
    super(User, dataSource);
    this.serviceNows = this.createHasManyRepositoryFactoryFor('serviceNows', serviceNowRepositoryGetter,);
    this.registerInclusionResolver('serviceNows', this.serviceNows.inclusionResolver);
    this.dscoApproverReports = this.createHasManyRepositoryFactoryFor('dscoApproverReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('dscoApproverReports', this.dscoApproverReports.inclusionResolver);
    this.approverReports = this.createHasManyRepositoryFactoryFor('approverReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('approverReports', this.approverReports.inclusionResolver);
    this.assessorReports = this.createHasManyRepositoryFactoryFor('assessorReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('assessorReports', this.assessorReports.inclusionResolver);
    this.applicantReports = this.createHasManyRepositoryFactoryFor('applicantReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('applicantReports', this.applicantReports.inclusionResolver);
    this.towerCranes = this.createHasManyRepositoryFactoryFor('towerCranes', towerCraneRepositoryGetter,);
    this.registerInclusionResolver('towerCranes', this.towerCranes.inclusionResolver);
    this.reportIncidents = this.createHasManyRepositoryFactoryFor('reportIncidents', reportIncidentRepositoryGetter,);
    this.registerInclusionResolver('reportIncidents', this.reportIncidents.inclusionResolver);
    this.userLocationRoles = this.createHasManyRepositoryFactoryFor('userLocationRoles', userLocationRoleRepositoryGetter,);
    this.registerInclusionResolver('userLocationRoles', this.userLocationRoles.inclusionResolver);
    this.userLocation = this.createHasOneRepositoryFactoryFor('userLocation', userLocationRepositoryGetter);
    this.registerInclusionResolver('userLocation', this.userLocation.inclusionResolver);
 
    this.ehsRole = this.createBelongsToAccessorFor('ehsRole', ehsRoleRepositoryGetter,);
    this.registerInclusionResolver('ehsRole', this.ehsRole.inclusionResolver);
 
 
    this.userCredentials = this.createHasOneRepositoryFactoryFor(
      'userCredentials',
      userCredentialsRepositoryGetter,
    );
  
  }

  async findCredentials(
    userId: typeof User.prototype.id,
  ): Promise<UserCredentials | undefined> {
    try {
      return await this.userCredentials(userId).get();
    } catch (err) {
      if (err.code === 'ENTITY_NOT_FOUND') {
        return undefined;
      }
      throw err;
    }
  }
}
