// Copyright IBM Corp. and LoopBack contributors 2020. All Rights Reserved.
// Node module: @loopback/example-file-transfer
// This file is licensed under the MIT License.
// License text available at https://opensource.org/licenses/MIT

import { inject, service } from '@loopback/core';
import {
  post,
  Request,
  requestBody,
  Response,
  RestBindings,
} from '@loopback/rest';
import { FILE_UPLOAD_SERVICE } from '../keys';
import { FileUploadHandler } from '../types';

const OAUTH_TOKEN_ENDPOINT = 'https://login.microsoftonline.com/d1dbb6ae-7ad0-4ac9-92dc-defb3673faaa/oauth2/token';
const CLIENT_ID = '1e88e022-b24b-456a-bc90-42972248d372';
const CLIENT_SECRET = '****************************************'; // Store this securely
const RESOURCE = 'https://graph.microsoft.com/';


import axios from 'axios';
/**
 * A controller to handle file uploads using multipart/form-data media type
 */
import { authenticate } from '@loopback/authentication';
// @authenticate('jwt')
export class FileUploadController {
  /**
   * Constructor
   * @param handler - Inject an express request handler to deal with the request
   */
  constructor(
    @inject(FILE_UPLOAD_SERVICE) private handler: FileUploadHandler,

  ) { }
  async getAccessToken() {
    const data = new URLSearchParams();
    data.append('grant_type', 'client_credentials');
    data.append('client_id', CLIENT_ID);
    data.append('client_secret', CLIENT_SECRET);
    data.append('resource', RESOURCE);

    try {
      const response = await axios.post(OAUTH_TOKEN_ENDPOINT, data, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      return response.data.access_token;
    } catch (error) {
      console.error('Error getting access token:', error);
      throw error;
    }
  }
  async uploadFileToSharePoint(file: any, siteId: any, driveId: any, accessToken: any) {
    const endpoint = `https://graph.microsoft.com/v1.0/drives/${driveId}/root:/${file.originalname}:/content`;

    const headers = {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': file.mimetype,
    };

    try {
      const response = await axios.put(endpoint, file.buffer, { headers });
      return response.data;
    } catch (error) {
      console.error('Error uploading file to SharePoint:', error);
      throw error;
    }
  }
  @post('/files', {
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
            },
          },
        },
        description: 'Files and fields',
      },
    },
  })
  async fileUpload(
    @requestBody.file()
    request: Request,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ): Promise<object> {
    return new Promise<object>((resolve, reject) => {
      this.handler(request, response, async (err: unknown) => {
        if (err) reject(err);
        else {

          const { files } = FileUploadController.getFilesAndFields(request);

          for (const file of files) {
            const siteId = '';
            const driveId = 'b!4x0l02NxqE-XiBZXtpMsz29n2W0tTGRFhPVdap_qYaRRsYDYgfupQKauSmE-5Nm0';
            const accessToken = await this.getAccessToken();
            const uploadResult = await this.uploadFileToSharePoint(file, siteId, driveId, accessToken);
            // console.log(uploadResult)

          }
          resolve(FileUploadController.getFilesAndFields(request));
        }
      });
    });
  }

  /**
   * Get files and fields for the request
   * @param request - Http request
   */
  private static getFilesAndFields(request: Request) {
    const uploadedFiles = request.files;
    const mapper = (f: globalThis.Express.Multer.File) => ({

      fieldname: f.fieldname,
      originalname: f.filename,
      encoding: f.encoding,
      mimetype: f.mimetype,
      size: f.size,
    });

    let files: object[] = [];
    if (Array.isArray(uploadedFiles)) {

      files = uploadedFiles.map(mapper);

    } else {

      for (const filename in uploadedFiles) {

        files.push(...uploadedFiles[filename].map(mapper));
      }
    }
    return { files, fields: request.body };
  }
}