import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {CustomName} from '../models';
import {CustomNameRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class CustomNameController {
  constructor(
    @repository(CustomNameRepository)
    public customNameRepository : CustomNameRepository,
  ) {}

  @post('/custom-names')
  @response(200, {
    description: 'CustomName model instance',
    content: {'application/json': {schema: getModelSchemaRef(CustomName)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CustomName, {
            title: 'NewCustomName',
            exclude: ['id'],
          }),
        },
      },
    })
    customName: Omit<CustomName, 'id'>,
  ): Promise<CustomName> {
    return this.customNameRepository.create(customName);
  }

  @get('/custom-names/count')
  @response(200, {
    description: 'CustomName model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(CustomName) where?: Where<CustomName>,
  ): Promise<Count> {
    return this.customNameRepository.count(where);
  }

  @get('/custom-names')
  @response(200, {
    description: 'Array of CustomName model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(CustomName, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(CustomName) filter?: Filter<CustomName>,
  ): Promise<CustomName[]> {
    return this.customNameRepository.find(filter);
  }

  @patch('/custom-names')
  @response(200, {
    description: 'CustomName PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CustomName, {partial: true}),
        },
      },
    })
    customName: CustomName,
    @param.where(CustomName) where?: Where<CustomName>,
  ): Promise<Count> {
    return this.customNameRepository.updateAll(customName, where);
  }

  @get('/custom-names/{id}')
  @response(200, {
    description: 'CustomName model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(CustomName, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(CustomName, {exclude: 'where'}) filter?: FilterExcludingWhere<CustomName>
  ): Promise<CustomName> {
    return this.customNameRepository.findById(id, filter);
  }

  @patch('/custom-names/{id}')
  @response(204, {
    description: 'CustomName PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CustomName, {partial: true}),
        },
      },
    })
    customName: CustomName,
  ): Promise<void> {
    await this.customNameRepository.updateById(id, customName);
  }

  @put('/custom-names/{id}')
  @response(204, {
    description: 'CustomName PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() customName: CustomName,
  ): Promise<void> {
    await this.customNameRepository.replaceById(id, customName);
  }

  @del('/custom-names/{id}')
  @response(204, {
    description: 'CustomName DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.customNameRepository.deleteById(id);
  }
}
