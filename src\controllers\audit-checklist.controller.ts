import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Audit,
  Checklist,
} from '../models';
import {AuditRepository} from '../repositories';

export class AuditChecklistController {
  constructor(
    @repository(AuditRepository)
    public auditRepository: AuditRepository,
  ) { }

  @get('/audits/{id}/checklist', {
    responses: {
      '200': {
        description: 'Checklist belonging to <PERSON>t',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Checklist)},
          },
        },
      },
    },
  })
  async getChecklist(
    @param.path.string('id') id: typeof Audit.prototype.id,
  ): Promise<Checklist> {
    return this.auditRepository.checklist(id);
  }
}
