import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationFive,
  ObservationReport,
} from '../models';
import {LocationFiveRepository} from '../repositories';

export class LocationFiveObservationReportController {
  constructor(
    @repository(LocationFiveRepository) protected locationFiveRepository: LocationFiveRepository,
  ) { }

  @get('/location-fives/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'Array of LocationFive has many ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ObservationReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.locationFiveRepository.observationReports(id).find(filter);
  }

  @post('/location-fives/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationFive model instance',
        content: {'application/json': {schema: getModelSchemaRef(ObservationReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationFive.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReportInLocationFive',
            exclude: ['id'],
            optional: ['locationFiveId']
          }),
        },
      },
    }) observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    return this.locationFiveRepository.observationReports(id).create(observationReport);
  }

  @patch('/location-fives/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationFive.ObservationReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: Partial<ObservationReport>,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationFiveRepository.observationReports(id).patch(observationReport, where);
  }

  @del('/location-fives/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationFive.ObservationReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationFiveRepository.observationReports(id).delete(where);
  }
}
