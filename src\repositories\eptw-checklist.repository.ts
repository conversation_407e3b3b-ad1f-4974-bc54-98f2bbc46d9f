import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {EptwChecklist, EptwChecklistRelations} from '../models';

export class EptwChecklistRepository extends DefaultCrudRepository<
  EptwChecklist,
  typeof EptwChecklist.prototype.id,
  EptwChecklistRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(EptwChecklist, dataSource);
  }
}
