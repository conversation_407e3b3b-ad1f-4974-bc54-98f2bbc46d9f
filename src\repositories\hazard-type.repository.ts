import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {HazardType, HazardTypeRelations, HazardDescription} from '../models';
import {HazardDescriptionRepository} from './hazard-description.repository';

export class HazardTypeRepository extends DefaultCrudRepository<
  HazardType,
  typeof HazardType.prototype.id,
  HazardTypeRelations
> {

  public readonly hazardDescriptions: HasManyRepositoryFactory<HazardDescription, typeof HazardType.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('HazardDescriptionRepository') protected hazardDescriptionRepositoryGetter: Getter<HazardDescriptionRepository>,
  ) {
    super(HazardType, dataSource);
    this.hazardDescriptions = this.createHasManyRepositoryFactoryFor('hazardDescriptions', hazardDescriptionRepositoryGetter,);
    this.registerInclusionResolver('hazardDescriptions', this.hazardDescriptions.inclusionResolver);
  }
}
