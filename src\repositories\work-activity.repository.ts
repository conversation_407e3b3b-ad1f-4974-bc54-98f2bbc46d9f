import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {WorkActivity, WorkActivityRelations, ObservationReport} from '../models';
import {ObservationReportRepository} from './observation-report.repository';

export class WorkActivityRepository extends DefaultCrudRepository<
  WorkActivity,
  typeof WorkActivity.prototype.id,
  WorkActivityRelations
> {

  public readonly observationReports: HasManyRepositoryFactory<ObservationReport, typeof WorkActivity.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('ObservationReportRepository') protected observationReportRepositoryGetter: Getter<ObservationReportRepository>,
  ) {
    super(WorkActivity, dataSource);
    this.observationReports = this.createHasManyRepositoryFactoryFor('observationReports', observationReportRepositoryGetter,);
    this.registerInclusionResolver('observationReports', this.observationReports.inclusionResolver);
  }
}
