import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ReportData, ReportDataRelations, LocationOne, LocationTwo, LocationThree, LocationFour, User} from '../models';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';
import {UserRepository} from './user.repository';

export class ReportDataRepository extends DefaultCrudRepository<
  ReportData,
  typeof ReportData.prototype.id,
  ReportDataRelations
> {

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof ReportData.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof ReportData.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof ReportData.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof ReportData.prototype.id>;

  public readonly user: BelongsToAccessor<User, typeof ReportData.prototype.id>;

  public readonly reviewer: BelongsToAccessor<User, typeof ReportData.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(ReportData, dataSource);
    this.reviewer = this.createBelongsToAccessorFor('reviewer', userRepositoryGetter,);
    this.registerInclusionResolver('reviewer', this.reviewer.inclusionResolver);
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter,);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
  }
}
