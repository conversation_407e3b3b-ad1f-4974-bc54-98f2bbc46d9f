import { Client } from '@microsoft/microsoft-graph-client';

export class GraphService {
    private graphClient: Client;

    constructor() {
        this.graphClient = Client.init({
            authProvider: (done) => {
                done(null, 'eyJ0eXAiOiJKV1QiLCJub25jZSI6IkR0Zl8wZjA0X0dkMmdxLUhYVHN6dU9QMExoQ2V0Rk12ckhiSTEwOHpoM2ciLCJhbGciOiJSUzI1NiIsIng1dCI6IjVCM25SeHRRN2ppOGVORGMzRnkwNUtmOTdaRSIsImtpZCI6IjVCM25SeHRRN2ppOGVORGMzRnkwNUtmOTdaRSJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bAUyTY9_A6gsuLl0hDObCDtoNbf9aNIvvqHi9AO2MesLNiinx3hAOL0sYH4vwlKs67OKshF0SEO5tP8PkbpgQ3rBR3aZO8EP-YF11PWnBi595_G6ad7yFn4rr1m9HI7g_366GAdJIj-a0zQWgW-nrhwW_GIvLgYExye5qiniCHoSr1USuNtzgKUn7h3h70SIwmEa0XhMj4T5jjDRYGBdMU8escNYEucJNJvoBNjuJkHyJAOQ6XxHCVrkx96L3pA9XqQD8DWfFI-36ntmp04ovEhCl5BkfAZVmLQyzHIHx-gExp6YZmHwIGpcdL6UwjS7gN9ZBoQPrV1y0qdVxHLHPA'); // Replace with your hard-coded token
            },
        });
    }

    // Method to upload a file to SharePoint
    async uploadFileToSharePoint(driveId: string, fileName: string, fileStream: NodeJS.ReadableStream) {
        const uploadPath = `/drives/${driveId}/root:/${fileName}:/content`;
        try {
            const response = await this.graphClient.api(uploadPath).put(fileStream);
            return response;
        } catch (error) {
            console.error('Error uploading file to SharePoint:', error);
            throw error;
        }
    }
}
