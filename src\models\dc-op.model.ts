import {Entity, model, property} from '@loopback/repository';

@model()
export class DcOp extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  locationOneId?: string;

  @property({
    type: 'string',
  })
  locationTwoId?: string;

  @property({
    type: 'string',
  })
  locationThreeId?: string;

  @property({
    type: 'string',
  })
  locationFourId?: string;

  @property({
    type: 'string',
  })
  locationFiveId?: string;

  @property({
    type: 'string',
  })
  locationSixId?: string;

  @property({
    type: 'string',
  })
  permitStartDate?: string;

  @property({
    type: 'string',
  })
  permitEndDate?: string;

  @property({
    type: 'object',
  })
  dcOpsData?: object;

  @property({
    type: 'string',
  })
  dcso?: string;

  @property({
    type: 'string',
  })
  involveHighRisk?: string;

  @property({
    type: 'object', 
  })
  highRiskData?: object;

  @property({
    type: 'string',
  })
  highRiskAssessor?: string;

  @property({
    type: 'string',
  })
  permitType?: string;

  @property({
    type: 'string',
  })
  applicantSign?: string;

  @property({
    type: 'string',
  })
  dcsoAssessorSign?: string;

  @property({
    type: 'string',
  })
  dcsoApproverSign?: string;

  @property({
    type: 'string',
  })
  submittedId?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  status?: string;


  constructor(data?: Partial<DcOp>) {
    super(data);
  }
}

export interface DcOpRelations {
  // describe navigational properties here
}

export type DcOpWithRelations = DcOp & DcOpRelations;
