import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationFour,
  LocationFive,
} from '../models';
import {LocationFourRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
// @authenticate('jwt')
export class LocationFourLocationFiveController {
  constructor(
    @repository(LocationFourRepository) protected locationFourRepository: LocationFourRepository,
  ) { }

  @get('/location-fours/{id}/location-fives', {
    responses: {
      '200': {
        description: 'Array of LocationFour has many LocationFive',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationFive)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<LocationFive>,
  ): Promise<LocationFive[]> {
    return this.locationFourRepository.locationFives(id).find(filter);
  }

  @post('/location-fours/{id}/location-fives', {
    responses: {
      '200': {
        description: 'LocationFour model instance',
        content: {'application/json': {schema: getModelSchemaRef(LocationFive)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationFour.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFive, {
            title: 'NewLocationFiveInLocationFour',
            exclude: ['id'],
            optional: ['locationFourId']
          }),
        },
      },
    }) locationFive: Omit<LocationFive, 'id'>,
  ): Promise<LocationFive> {
    return this.locationFourRepository.locationFives(id).create(locationFive);
  }

  @patch('/location-fours/{id}/location-fives', {
    responses: {
      '200': {
        description: 'LocationFour.LocationFive PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFive, {partial: true}),
        },
      },
    })
    locationFive: Partial<LocationFive>,
    @param.query.object('where', getWhereSchemaFor(LocationFive)) where?: Where<LocationFive>,
  ): Promise<Count> {
    return this.locationFourRepository.locationFives(id).patch(locationFive, where);
  }

  @del('/location-fours/{id}/location-fives', {
    responses: {
      '200': {
        description: 'LocationFour.LocationFive DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(LocationFive)) where?: Where<LocationFive>,
  ): Promise<Count> {
    return this.locationFourRepository.locationFives(id).delete(where);
  }
}
