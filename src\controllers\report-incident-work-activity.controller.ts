import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  WorkActivity,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentWorkActivityController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/work-activity', {
    responses: {
      '200': {
        description: 'WorkActivity belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(WorkActivity)},
          },
        },
      },
    },
  })
  async getWorkActivity(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<WorkActivity> {
    return this.reportIncidentRepository.workActivity(id);
  }
}
