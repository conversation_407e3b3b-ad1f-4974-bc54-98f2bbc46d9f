import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {HumanOne} from '../models';
import {HumanOneRepository} from '../repositories';

export class HumanOneController {
  constructor(
    @repository(HumanOneRepository)
    public humanOneRepository : HumanOneRepository,
  ) {}

  @post('/human-ones')
  @response(200, {
    description: 'HumanOne model instance',
    content: {'application/json': {schema: getModelSchemaRef(HumanOne)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanOne, {
            title: 'NewHumanOne',
            exclude: ['id'],
          }),
        },
      },
    })
    humanOne: Omit<HumanOne, 'id'>,
  ): Promise<HumanOne> {
    return this.humanOneRepository.create(humanOne);
  }

  @get('/human-ones/count')
  @response(200, {
    description: 'HumanOne model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(HumanOne) where?: Where<HumanOne>,
  ): Promise<Count> {
    return this.humanOneRepository.count(where);
  }

  @get('/human-ones')
  @response(200, {
    description: 'Array of HumanOne model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(HumanOne, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(HumanOne) filter?: Filter<HumanOne>,
  ): Promise<HumanOne[]> {
    return this.humanOneRepository.find(filter);
  }

  @patch('/human-ones')
  @response(200, {
    description: 'HumanOne PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanOne, {partial: true}),
        },
      },
    })
    humanOne: HumanOne,
    @param.where(HumanOne) where?: Where<HumanOne>,
  ): Promise<Count> {
    return this.humanOneRepository.updateAll(humanOne, where);
  }

  @get('/human-ones/{id}')
  @response(200, {
    description: 'HumanOne model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(HumanOne, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(HumanOne, {exclude: 'where'}) filter?: FilterExcludingWhere<HumanOne>
  ): Promise<HumanOne> {
    return this.humanOneRepository.findById(id, filter);
  }

  @patch('/human-ones/{id}')
  @response(204, {
    description: 'HumanOne PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanOne, {partial: true}),
        },
      },
    })
    humanOne: HumanOne,
  ): Promise<void> {
    await this.humanOneRepository.updateById(id, humanOne);
  }

  @put('/human-ones/{id}')
  @response(204, {
    description: 'HumanOne PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() humanOne: HumanOne,
  ): Promise<void> {
    await this.humanOneRepository.replaceById(id, humanOne);
  }

  @del('/human-ones/{id}')
  @response(204, {
    description: 'HumanOne DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.humanOneRepository.deleteById(id);
  }
}
