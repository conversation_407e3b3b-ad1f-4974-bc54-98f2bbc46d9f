import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {Inspection, InspectionRelations, Checklist, User, LocationOne, LocationTwo, LocationThree, LocationFour, Action} from '../models';
import {ChecklistRepository} from './checklist.repository';
import {UserRepository} from './user.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';
import {ActionRepository} from './action.repository';

export class InspectionRepository extends DefaultCrudRepository<
  Inspection,
  typeof Inspection.prototype.id,
  InspectionRelations
> {

  public readonly checklist: BelongsToAccessor<Checklist, typeof Inspection.prototype.id>;

  public readonly assignedTo: BelongsToAccessor<User, typeof Inspection.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof Inspection.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof Inspection.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof Inspection.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof Inspection.prototype.id>;

  public readonly actions: HasManyRepositoryFactory<Action, typeof Inspection.prototype.id>;

  public readonly approver: BelongsToAccessor<User, typeof Inspection.prototype.id>;

  public readonly assignedBy: BelongsToAccessor<User, typeof Inspection.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('ChecklistRepository') protected checklistRepositoryGetter: Getter<ChecklistRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('ActionRepository') protected actionRepositoryGetter: Getter<ActionRepository>,
  ) {
    super(Inspection, dataSource);
    this.assignedBy = this.createBelongsToAccessorFor('assignedBy', userRepositoryGetter,);
    this.registerInclusionResolver('assignedBy', this.assignedBy.inclusionResolver);
    this.approver = this.createBelongsToAccessorFor('approver', userRepositoryGetter,);
    this.registerInclusionResolver('approver', this.approver.inclusionResolver);
    this.actions = this.createHasManyRepositoryFactoryFor('actions', actionRepositoryGetter,);
    this.registerInclusionResolver('actions', this.actions.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.assignedTo = this.createBelongsToAccessorFor('assignedTo', userRepositoryGetter,);
    this.registerInclusionResolver('assignedTo', this.assignedTo.inclusionResolver);
    this.checklist = this.createBelongsToAccessorFor('checklist', checklistRepositoryGetter,);
    this.registerInclusionResolver('checklist', this.checklist.inclusionResolver);
  }
}
