import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Inspection,
  LocationThree,
} from '../models';
import {InspectionRepository} from '../repositories';

export class InspectionLocationThreeController {
  constructor(
    @repository(InspectionRepository)
    public inspectionRepository: InspectionRepository,
  ) { }

  @get('/inspections/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to Inspection',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationThree)},
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof Inspection.prototype.id,
  ): Promise<LocationThree> {
    return this.inspectionRepository.locationThree(id);
  }
}
