import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  WorkActivity,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportWorkActivityController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/work-activity', {
    responses: {
      '200': {
        description: 'WorkActivity belonging to ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(WorkActivity)},
          },
        },
      },
    },
  })
  async getWorkActivity(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<WorkActivity> {
    return this.observationReportRepository.workActivity(id);
  }
}
