import { Entity, model, property } from '@loopback/repository';

@model()
export class ReportRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;
  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  permissions?: any;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
  })
  created?: string;

  constructor(data?: Partial<ReportRole>) {
    super(data);
  }
}

export interface ReportRoleRelations {
  // describe navigational properties here
}

export type ReportRoleWithRelations = ReportRole & ReportRoleRelations;
