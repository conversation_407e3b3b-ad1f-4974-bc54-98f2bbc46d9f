import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ServiceNow, ServiceNowRelations, User} from '../models';
import {UserRepository} from './user.repository';

export class ServiceNowRepository extends DefaultCrudRepository<
  ServiceNow,
  typeof ServiceNow.prototype.id,
  ServiceNowRelations
> {

  public readonly user: BelongsToAccessor<User, typeof ServiceNow.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(ServiceNow, dataSource);
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter,);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
  }
}
