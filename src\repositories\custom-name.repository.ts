import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {CustomName, CustomNameRelations} from '../models';

export class CustomNameRepository extends DefaultCrudRepository<
  CustomName,
  typeof CustomName.prototype.id,
  CustomNameRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(CustomName, dataSource);
  }
}
