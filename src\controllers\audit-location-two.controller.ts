import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Audit,
  LocationTwo,
} from '../models';
import {AuditRepository} from '../repositories';

export class AuditLocationTwoController {
  constructor(
    @repository(AuditRepository)
    public auditRepository: AuditRepository,
  ) { }

  @get('/audits/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to Audit',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationTwo)},
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof Audit.prototype.id,
  ): Promise<LocationTwo> {
    return this.auditRepository.locationTwo(id);
  }
}
