import { Entity, model, property } from '@loopback/repository';

@model()
export class EptwChecklist extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  label: string;

  @property({
    type: 'array',
    itemType: 'number',
    default: [],
  })
  applicable: number[];

  @property({
    type: 'boolean',
    default: false,
  })
  attachment?: boolean;

  @property({
    type: 'string',
    default: '',
  })
  attachText?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  user?: boolean;

  @property({
    type: 'string',
    default: '',
  })
  userText?: string;

  @property({
    type: 'array',
    itemType: 'object',
    default: [],
  })
  option?: object[];

  constructor(data?: Partial<EptwChecklist>) {
    super(data);
  }
}

export interface EptwChecklistRelations {
  // describe navigational properties here
}

export type EptwChecklistWithRelations = EptwChecklist & EptwChecklistRelations;
