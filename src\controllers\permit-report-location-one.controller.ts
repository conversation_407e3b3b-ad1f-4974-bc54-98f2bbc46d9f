import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  PermitReport,
  LocationOne,
} from '../models';
import {PermitReportRepository} from '../repositories';

export class PermitReportLocationOneController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
  ) { }

  @get('/permit-reports/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to PermitReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationOne)},
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof PermitReport.prototype.id,
  ): Promise<LocationOne> {
    return this.permitReportRepository.locationOne(id);
  }
}
