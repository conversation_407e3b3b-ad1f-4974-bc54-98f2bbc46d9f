import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  User,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentUserController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<User> {
    return this.reportIncidentRepository.reviewer(id);
  }
}
