import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  GhsTwo,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportGhsTwoController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/ghs-two', {
    responses: {
      '200': {
        description: 'GhsTwo belonging to ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(GhsTwo)},
          },
        },
      },
    },
  })
  async getGhsTwo(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<GhsTwo> {
    return this.observationReportRepository.ghsTwo(id);
  }
}
