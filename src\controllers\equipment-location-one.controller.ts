import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Equipment,
  LocationOne,
} from '../models';
import {EquipmentRepository} from '../repositories';

export class EquipmentLocationOneController {
  constructor(
    @repository(EquipmentRepository)
    public equipmentRepository: EquipmentRepository,
  ) { }

  @get('/equipment/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to Equipment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationOne)},
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof Equipment.prototype.id,
  ): Promise<LocationOne> {
    return this.equipmentRepository.locationOne(id);
  }
}
