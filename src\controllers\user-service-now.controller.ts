import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  User,
  ServiceNow,
} from '../models';
import {UserRepository} from '../repositories';

export class UserServiceNowController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
  ) { }

  @get('/users/{id}/service-nows', {
    responses: {
      '200': {
        description: 'Array of User has many ServiceNow',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ServiceNow)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ServiceNow>,
  ): Promise<ServiceNow[]> {
    return this.userRepository.serviceNows(id).find(filter);
  }

  @post('/users/{id}/service-nows', {
    responses: {
      '200': {
        description: 'User model instance',
        content: {'application/json': {schema: getModelSchemaRef(ServiceNow)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof User.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ServiceNow, {
            title: 'NewServiceNowInUser',
            exclude: ['id'],
            optional: ['userId']
          }),
        },
      },
    }) serviceNow: Omit<ServiceNow, 'id'>,
  ): Promise<ServiceNow> {
    return this.userRepository.serviceNows(id).create(serviceNow);
  }

  @patch('/users/{id}/service-nows', {
    responses: {
      '200': {
        description: 'User.ServiceNow PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ServiceNow, {partial: true}),
        },
      },
    })
    serviceNow: Partial<ServiceNow>,
    @param.query.object('where', getWhereSchemaFor(ServiceNow)) where?: Where<ServiceNow>,
  ): Promise<Count> {
    return this.userRepository.serviceNows(id).patch(serviceNow, where);
  }

  @del('/users/{id}/service-nows', {
    responses: {
      '200': {
        description: 'User.ServiceNow DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ServiceNow)) where?: Where<ServiceNow>,
  ): Promise<Count> {
    return this.userRepository.serviceNows(id).delete(where);
  }
}
