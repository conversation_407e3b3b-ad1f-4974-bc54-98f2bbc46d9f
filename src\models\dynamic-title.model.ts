import {Entity, model, property} from '@loopback/repository';

@model()
export class DynamicTitle extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  altTitle?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;


  constructor(data?: Partial<DynamicTitle>) {
    super(data);
  }
}

export interface DynamicTitleRelations {
  // describe navigational properties here
}

export type DynamicTitleWithRelations = DynamicTitle & DynamicTitleRelations;
