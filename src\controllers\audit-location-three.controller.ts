import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Audit,
  LocationThree,
} from '../models';
import {AuditRepository} from '../repositories';

export class AuditLocationThreeController {
  constructor(
    @repository(AuditRepository)
    public auditRepository: AuditRepository,
  ) { }

  @get('/audits/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to Audit',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationThree)},
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof Audit.prototype.id,
  ): Promise<LocationThree> {
    return this.auditRepository.locationThree(id);
  }
}
