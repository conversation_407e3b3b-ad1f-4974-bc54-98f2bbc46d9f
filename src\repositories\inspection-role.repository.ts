import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {InspectionRole, InspectionRoleRelations} from '../models';

export class InspectionRoleRepository extends DefaultCrudRepository<
  InspectionRole,
  typeof InspectionRole.prototype.id,
  InspectionRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(InspectionRole, dataSource);
  }
}
