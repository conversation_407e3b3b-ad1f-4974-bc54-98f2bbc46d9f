declare module 'pdfmake' {
    export default class PdfPrinter {
      constructor(fontDescriptors: FontDescriptors);
  
      public createPdfKitDocument(
        docDefinition: TDocumentDefinitions,
        options?: TDocumentOptions,
        tableLayouts?: { [name: string]: CustomTableLayout }
      ): any;
    }
  
    export interface FontDescriptors {
      [fontName: string]: {
        normal?: string;
        bold?: string;
        italics?: string;
        bolditalics?: string;
      };
    }
  
    export interface TDocumentDefinitions {
      content: any;
      styles?: any;
      defaultStyle?: any;
      pageSize?: string | { width: number; height: number };
      pageOrientation?: 'portrait' | 'landscape';
      pageMargins?: [number, number, number, number];
      header?: any;
      footer?: any;
      background?: any;
      watermark?: any;
      info?: any;
    }
  
    export interface TDocumentOptions {
      [option: string]: any;
    }
  
    export interface CustomTableLayout {
      [key: string]: any;
    }
  }
  