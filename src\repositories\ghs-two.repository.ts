import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GhsTwo, GhsTwoRelations, ObservationReport} from '../models';
import {ObservationReportRepository} from './observation-report.repository';

export class GhsTwoRepository extends DefaultCrudRepository<
  GhsTwo,
  typeof GhsTwo.prototype.id,
  GhsTwoRelations
> {

  public readonly observationReports: HasManyRepositoryFactory<ObservationReport, typeof GhsTwo.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('ObservationReportRepository') protected observationReportRepositoryGetter: Getter<ObservationReportRepository>,
  ) {
    super(GhsTwo, dataSource);
    this.observationReports = this.createHasManyRepositoryFactoryFor('observationReports', observationReportRepositoryGetter,);
    this.registerInclusionResolver('observationReports', this.observationReports.inclusionResolver);
  }
}
