import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  UserLocationRole,
  LocationThree,
} from '../models';
import {UserLocationRoleRepository} from '../repositories';

export class UserLocationRoleLocationThreeController {
  constructor(
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
  ) { }

  @get('/user-location-roles/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to UserLocationRole',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationThree)},
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof UserLocationRole.prototype.id,
  ): Promise<LocationThree> {
    return this.userLocationRoleRepository.locationThree(id);
  }
}
