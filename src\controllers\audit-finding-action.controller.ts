import {
  Count,
  CountSchema,
  Filter,
  model,
  property,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AuditFinding,
  Action,
} from '../models';
import { AuditFindingRepository } from '../repositories';


export class AuditFindingActionController {
  constructor(
    @repository(AuditFindingRepository) protected auditFindingRepository: AuditFindingRepository,
  ) { }

  @get('/audit-findings/{id}/actions', {
    responses: {
      '200': {
        description: 'Array of AuditFinding has many Action',
        content: {
          'application/json': {
            schema: { type: 'array', items: getModelSchemaRef(Action) },
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Action>,
  ): Promise<Action[]> {
    return this.auditFindingRepository.actions(id).find(filter);
  }

  @post('/audit-findings/{id}/actions', {
    responses: {
      '200': {
        description: 'AuditFinding model instance',
        content: { 'application/json': { schema: getModelSchemaRef(Action) } },
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof AuditFinding.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {
            title: 'NewActionInAuditFinding',
            exclude: ['id'],
            optional: ['objectId']
          }),
        },
      },
    }) action: Omit<Action, 'id'>,
  ): Promise<Action> {
    return this.auditFindingRepository.actions(id).create(action);
  }

  @patch('/audit-findings/{id}/actions', {
    responses: {
      '200': {
        description: 'AuditFinding.Action PATCH success count',
        content: { 'application/json': { schema: CountSchema } },
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, { partial: true }),
        },
      },
    })
    action: Partial<Action>,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.auditFindingRepository.actions(id).patch(action, where);
  }

  @del('/audit-findings/{id}/actions', {
    responses: {
      '200': {
        description: 'AuditFinding.Action DELETE success count',
        content: { 'application/json': { schema: CountSchema } },
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.auditFindingRepository.actions(id).delete(where);
  }
}
