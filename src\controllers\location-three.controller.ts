import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { LocationThree } from '../models';
import { LocationThreeRepository } from '../repositories';
import { authenticate } from '@loopback/authentication';
// @authenticate('jwt')
export class LocationThreeController {
  constructor(
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
  ) { }

  @post('/location-threes')
  @response(200, {
    description: 'LocationThree model instance',
    content: { 'application/json': { schema: getModelSchemaRef(LocationThree) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationThree, {
            title: 'NewLocationThree',
            exclude: ['id'],
          }),
        },
      },
    })
    locationThree: Omit<LocationThree, 'id'>,
  ): Promise<LocationThree> {
    return this.locationThreeRepository.create(locationThree);
  }

  @get('/location-threes/count')
  @response(200, {
    description: 'LocationThree model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(LocationThree) where?: Where<LocationThree>,
  ): Promise<Count> {
    return this.locationThreeRepository.count(where);
  }

  @get('/location-threes')
  @response(200, {
    description: 'Array of LocationThree model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              title: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(LocationThree) filter?: Filter<LocationThree>,
  ): Promise<{ id: string; title: string }[]> {
    const locationThrees = await this.locationThreeRepository.find(filter);

    // Transform locationThrees into the desired format
    const transformedLocationThrees = locationThrees.map(locationThree => ({
      id: locationThree.id ?? '',
      title: locationThree.name ?? '',
      ...locationThree
    }));

    return transformedLocationThrees;
  }

  @patch('/location-threes')
  @response(200, {
    description: 'LocationThree PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationThree, { partial: true }),
        },
      },
    })
    locationThree: LocationThree,
    @param.where(LocationThree) where?: Where<LocationThree>,
  ): Promise<Count> {
    return this.locationThreeRepository.updateAll(locationThree, where);
  }

  @get('/location-threes/{id}')
  @response(200, {
    description: 'LocationThree model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(LocationThree, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(LocationThree, { exclude: 'where' }) filter?: FilterExcludingWhere<LocationThree>
  ): Promise<LocationThree> {
    return this.locationThreeRepository.findById(id, filter);
  }

  @patch('/location-threes/{id}')
  @response(204, {
    description: 'LocationThree PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationThree, { partial: true }),
        },
      },
    })
    locationThree: LocationThree,
  ): Promise<void> {
    await this.locationThreeRepository.updateById(id, locationThree);
  }

  @put('/location-threes/{id}')
  @response(204, {
    description: 'LocationThree PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() locationThree: LocationThree,
  ): Promise<void> {
    await this.locationThreeRepository.replaceById(id, locationThree);
  }

  @del('/location-threes/{id}')
  @response(204, {
    description: 'LocationThree DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.locationThreeRepository.deleteById(id);
  }
}
