import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AuditGmsTwo,
  AuditGmsThree,
} from '../models';
import {AuditGmsTwoRepository} from '../repositories';

export class AuditGmsTwoAuditGmsThreeController {
  constructor(
    @repository(AuditGmsTwoRepository) protected auditGmsTwoRepository: AuditGmsTwoRepository,
  ) { }

  @get('/audit-gms-twos/{id}/audit-gms-threes', {
    responses: {
      '200': {
        description: 'Array of AuditGmsTwo has many AuditGmsThree',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AuditGmsThree)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<AuditGmsThree>,
  ): Promise<AuditGmsThree[]> {
    return this.auditGmsTwoRepository.auditGmsThrees(id).find(filter);
  }

  @post('/audit-gms-twos/{id}/audit-gms-threes', {
    responses: {
      '200': {
        description: 'AuditGmsTwo model instance',
        content: {'application/json': {schema: getModelSchemaRef(AuditGmsThree)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof AuditGmsTwo.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsThree, {
            title: 'NewAuditGmsThreeInAuditGmsTwo',
            exclude: ['id'],
            optional: ['auditGmsTwoId']
          }),
        },
      },
    }) auditGmsThree: Omit<AuditGmsThree, 'id'>,
  ): Promise<AuditGmsThree> {
    return this.auditGmsTwoRepository.auditGmsThrees(id).create(auditGmsThree);
  }

  @patch('/audit-gms-twos/{id}/audit-gms-threes', {
    responses: {
      '200': {
        description: 'AuditGmsTwo.AuditGmsThree PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsThree, {partial: true}),
        },
      },
    })
    auditGmsThree: Partial<AuditGmsThree>,
    @param.query.object('where', getWhereSchemaFor(AuditGmsThree)) where?: Where<AuditGmsThree>,
  ): Promise<Count> {
    return this.auditGmsTwoRepository.auditGmsThrees(id).patch(auditGmsThree, where);
  }

  @del('/audit-gms-twos/{id}/audit-gms-threes', {
    responses: {
      '200': {
        description: 'AuditGmsTwo.AuditGmsThree DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(AuditGmsThree)) where?: Where<AuditGmsThree>,
  ): Promise<Count> {
    return this.auditGmsTwoRepository.auditGmsThrees(id).delete(where);
  }
}
