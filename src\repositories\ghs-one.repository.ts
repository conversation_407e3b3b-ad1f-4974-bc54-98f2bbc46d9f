import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GhsOne, GhsOneRelations, GhsTwo, ObservationReport} from '../models';
import {GhsTwoRepository} from './ghs-two.repository';
import {ObservationReportRepository} from './observation-report.repository';

export class GhsOneRepository extends DefaultCrudRepository<
  GhsOne,
  typeof GhsOne.prototype.id,
  GhsOneRelations
> {

  public readonly ghsTwos: HasManyRepositoryFactory<GhsTwo, typeof GhsOne.prototype.id>;

  public readonly observationReports: HasManyRepositoryFactory<ObservationReport, typeof GhsOne.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('GhsTwoRepository') protected ghsTwoRepositoryGetter: Getter<GhsTwoRepository>, @repository.getter('ObservationReportRepository') protected observationReportRepositoryGetter: Getter<ObservationReportRepository>,
  ) {
    super(GhsOne, dataSource);
    this.observationReports = this.createHasManyRepositoryFactoryFor('observationReports', observationReportRepositoryGetter,);
    this.registerInclusionResolver('observationReports', this.observationReports.inclusionResolver);
    this.ghsTwos = this.createHasManyRepositoryFactoryFor('ghsTwos', ghsTwoRepositoryGetter,);
    this.registerInclusionResolver('ghsTwos', this.ghsTwos.inclusionResolver);
  }
}
