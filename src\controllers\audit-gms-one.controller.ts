import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AuditGmsOne} from '../models';
import {AuditGmsOneRepository} from '../repositories';

export class AuditGmsOneController {
  constructor(
    @repository(AuditGmsOneRepository)
    public auditGmsOneRepository : AuditGmsOneRepository,
  ) {}

  @post('/audit-gms-ones')
  @response(200, {
    description: 'AuditGmsOne model instance',
    content: {'application/json': {schema: getModelSchemaRef(AuditGmsOne)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsOne, {
            title: 'NewAuditGmsOne',
            exclude: ['id'],
          }),
        },
      },
    })
    auditGmsOne: Omit<AuditGmsOne, 'id'>,
  ): Promise<AuditGmsOne> {
    return this.auditGmsOneRepository.create(auditGmsOne);
  }

  @get('/audit-gms-ones/count')
  @response(200, {
    description: 'AuditGmsOne model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AuditGmsOne) where?: Where<AuditGmsOne>,
  ): Promise<Count> {
    return this.auditGmsOneRepository.count(where);
  }

  @get('/audit-gms-ones')
  @response(200, {
    description: 'Array of AuditGmsOne model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AuditGmsOne, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AuditGmsOne) filter?: Filter<AuditGmsOne>,
  ): Promise<AuditGmsOne[]> {
    return this.auditGmsOneRepository.find(filter);
  }

  @patch('/audit-gms-ones')
  @response(200, {
    description: 'AuditGmsOne PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsOne, {partial: true}),
        },
      },
    })
    auditGmsOne: AuditGmsOne,
    @param.where(AuditGmsOne) where?: Where<AuditGmsOne>,
  ): Promise<Count> {
    return this.auditGmsOneRepository.updateAll(auditGmsOne, where);
  }

  @get('/audit-gms-ones/{id}')
  @response(200, {
    description: 'AuditGmsOne model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AuditGmsOne, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AuditGmsOne, {exclude: 'where'}) filter?: FilterExcludingWhere<AuditGmsOne>
  ): Promise<AuditGmsOne> {
    return this.auditGmsOneRepository.findById(id, filter);
  }

  @patch('/audit-gms-ones/{id}')
  @response(204, {
    description: 'AuditGmsOne PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsOne, {partial: true}),
        },
      },
    })
    auditGmsOne: AuditGmsOne,
  ): Promise<void> {
    await this.auditGmsOneRepository.updateById(id, auditGmsOne);
  }

  @put('/audit-gms-ones/{id}')
  @response(204, {
    description: 'AuditGmsOne PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() auditGmsOne: AuditGmsOne,
  ): Promise<void> {
    await this.auditGmsOneRepository.replaceById(id, auditGmsOne);
  }

  @del('/audit-gms-ones/{id}')
  @response(204, {
    description: 'AuditGmsOne DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.auditGmsOneRepository.deleteById(id);
  }
}
