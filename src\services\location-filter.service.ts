import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
  UserLocationRoleRepository,
  LocationOneRepository,
  LocationTwoRepository,
  LocationThreeRepository,
  LocationFourRepository,
} from '../repositories';

@injectable({scope: BindingScope.TRANSIENT})
export class LocationFilterService {
  constructor(
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
  ) {}

  async getLocationFilterConditions(userId: string): Promise<any[][]> {
    const userRoles = await this.userLocationRoleRepository.find({
      where: {userId},
    });

    const filterConditions = await Promise.all(
      userRoles.map(async userLocation => {
        const conditions: any[] = [];

        if (userLocation.locationOneId) {
          let locationOneIds: string[] = [];

          if (userLocation.locationOneId === 'tier1-all') {
            const allLocationOnes = await this.locationOneRepository.find();
            locationOneIds = allLocationOnes.map(loc => loc.id).filter((id): id is string => Boolean(id));
          } else {
            locationOneIds.push(userLocation.locationOneId);
          }

          if (locationOneIds.length > 0) {
            conditions.push({locationOneId: {inq: locationOneIds}});
          }
        }

        if (userLocation.locationTwoId) {
          let locationTwoIds: string[] = [];

          if (userLocation.locationTwoId === 'tier2-all') {
            const allLocationTwos = await this.locationTwoRepository.find({
              where: {locationOneId: userLocation.locationOneId},
            });
            locationTwoIds = allLocationTwos.map(loc => loc.id).filter((id): id is string => Boolean(id));
          } else {
            locationTwoIds.push(userLocation.locationTwoId);
          }

          if (locationTwoIds.length > 0) {
            conditions.push({locationTwoId: {inq: locationTwoIds}});
          }
        }

        if (userLocation.locationThreeId) {
          let locationThreeIds: string[] = [];

          if (userLocation.locationThreeId === 'tier3-all') {
            const allLocationThrees = await this.locationThreeRepository.find({
              where: {locationTwoId: userLocation.locationTwoId},
            });
            locationThreeIds = allLocationThrees.map(loc => loc.id).filter((id): id is string => Boolean(id));
          } else {
            locationThreeIds.push(userLocation.locationThreeId);
          }

          if (locationThreeIds.length > 0) {
            conditions.push({locationThreeId: {inq: locationThreeIds}});
          }
        }

        if (userLocation.locationFourId) {
          let locationFourIds: string[] = [];

          if (userLocation.locationFourId === 'tier4-all') {
            const allLocationFours = await this.locationFourRepository.find({
              where: {locationThreeId: userLocation.locationThreeId},
            });
            locationFourIds = allLocationFours.map(loc => loc.id).filter((id): id is string => Boolean(id));
          } else {
            locationFourIds.push(userLocation.locationFourId);
          }

          if (locationFourIds.length > 0) {
            conditions.push({locationFourId: {inq: locationFourIds}});
          }
        }

        return conditions.length > 0 ? conditions : null;
      })
    );

    // Filter out any null or empty condition arrays
    return filterConditions.filter((conditions): conditions is any[] => conditions !== null);
  }
}
