import { Entity, model, property, hasMany} from '@loopback/repository';
import {AuditFinding} from './audit-finding.model';

@model()
export class AuditGmsThree extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;
  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  auditGmsTwoId?: string;

  @hasMany(() => AuditFinding)
  auditFindings: AuditFinding[];

  constructor(data?: Partial<AuditGmsThree>) {
    super(data);
  }
}

export interface AuditGmsThreeRelations {
  // describe navigational properties here
}

export type AuditGmsThreeWithRelations = AuditGmsThree & AuditGmsThreeRelations;
