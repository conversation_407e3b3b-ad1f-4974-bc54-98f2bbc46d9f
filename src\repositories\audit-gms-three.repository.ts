import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {AuditGmsThree, AuditGmsThreeRelations, AuditFinding} from '../models';
import {AuditFindingRepository} from './audit-finding.repository';

export class AuditGmsThreeRepository extends DefaultCrudRepository<
  AuditGmsThree,
  typeof AuditGmsThree.prototype.id,
  AuditGmsThreeRelations
> {

  public readonly auditFindings: HasManyRepositoryFactory<AuditFinding, typeof AuditGmsThree.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('AuditFindingRepository') protected auditFindingRepositoryGetter: Getter<AuditFindingRepository>,
  ) {
    super(AuditGmsThree, dataSource);
    this.auditFindings = this.createHasManyRepositoryFactoryFor('auditFindings', auditFindingRepositoryGetter,);
    this.registerInclusionResolver('auditFindings', this.auditFindings.inclusionResolver);
  }
}
