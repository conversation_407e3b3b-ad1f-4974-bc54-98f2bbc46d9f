import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
  post,
  response,
  requestBody,
} from '@loopback/rest';
import {
  UserLocationRole,
  User,
} from '../models';
import { UserLocationRoleRepository, UserRepository } from '../repositories';

export class UserLocationRoleUserController {
  constructor(
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @get('/user-location-roles/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to UserLocationRole',
        content: {
          'application/json': {
            schema: { type: 'array', items: getModelSchemaRef(User) },
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof UserLocationRole.prototype.id,
  ): Promise<User> {
    return this.userLocationRoleRepository.user(id);
  }


  @post('/user-location-roles')
  @response(200, {
    description: 'UserLocationRole model instance',
    content: { 'application/json': { schema: getModelSchemaRef(UserLocationRole) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userIds: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              deselectUserIds: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              roles: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              locations: {
                type: 'object',
                properties: {
                  locationOne: {
                    type: 'string',
                  },
                  locationTwo: {
                    type: 'string',
                  },
                  locationThree: {
                    type: 'string',
                  },
                  locationFour: {
                    type: 'string',

                  }
                  // Repeat the pattern for locationThree, locationFour, locationFive, and locationSix
                },
              },
            },
          },
        },
      },
    })
    requestBody: { userIds: string[], deselectUserIds: string[], roles: string[], locations: { [key: string]: string } },
  ): Promise<any> {
    console.log(requestBody)
    // return this.userLocationRoleRepository.create(userLocationRole);
    const { userIds, deselectUserIds, roles, locations } = requestBody;
    const savedUserLocationRoles: UserLocationRole[] = [];
    for (const userId of userIds) {
      const existingUserLocationRole = await this.userLocationRoleRepository.findOne({
        where: {
          userId: userId,
          locationOneId: locations.locationOne,
          locationTwoId: locations.locationTwo,
          locationThreeId: locations.locationThree,
          locationFourId: locations.locationFour,
        },
      });
      if (existingUserLocationRole) {

        if (existingUserLocationRole.roles) {
          const combinedRoles = [...new Set(existingUserLocationRole.roles.concat(roles))];
          existingUserLocationRole.roles = combinedRoles;
        }

        await this.userLocationRoleRepository.updateById(existingUserLocationRole.id, existingUserLocationRole);
        savedUserLocationRoles.push(existingUserLocationRole);
      } else {
        const userLocationRole = new UserLocationRole();
        userLocationRole.userId = userId;
        userLocationRole.roles = roles;
        userLocationRole.locationOneId = locations.locationOne;
        userLocationRole.locationTwoId = locations.locationTwo;
        userLocationRole.locationThreeId = locations.locationThree;
        userLocationRole.locationFourId = locations.locationFour;
        const savedUserLocationRole = await this.userLocationRoleRepository.create(userLocationRole);
        savedUserLocationRoles.push(savedUserLocationRole);
      }
    }

    for (const userId of deselectUserIds) {
      const existingUserLocationRole = await this.userLocationRoleRepository.findOne({
        where: {
          userId: userId,
          locationOneId: locations.locationOne,
          locationTwoId: locations.locationTwo,
          locationThreeId: locations.locationThree,
          locationFourId: locations.locationFour,
        },
      });

      if (existingUserLocationRole) {

        if (existingUserLocationRole.roles) {
          const filteredRoles = existingUserLocationRole.roles.filter((role) => !roles.includes(role));
          existingUserLocationRole.roles = filteredRoles;
        }

        await this.userLocationRoleRepository.updateById(existingUserLocationRole.id, existingUserLocationRole);
        savedUserLocationRoles.push(existingUserLocationRole);
      }
    }

    return savedUserLocationRoles;
  }


  @post('/user-location-roles/get-users')
  @response(200, {
    description: 'List of users under the specified locations with roles',
    content: { 'application/json': { schema: { type: 'array', items: { 'x-ts-type': User } } } },
  })
  async getUsers(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              roles: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              locations: {
                type: 'object',
                properties: {
                  locationOne: { type: 'string' },
                  locationTwo: { type: 'string' },
                  locationThree: { type: 'string' },
                  locationFour: { type: 'string' },
                },
              },
            },
          },
        },
      },
    })
    requestBody: { roles: string[], locations: { [key: string]: string } },
  ): Promise<User[]> {
    console.log(requestBody);

    const { roles, locations } = requestBody;
    const { locationOne, locationTwo, locationThree, locationFour } = locations;

    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [roles] },
      or: [
        {
          locationOneId: { inq: [locationOne] },
          locationTwoId: { inq: [locationTwo] },
          locationThreeId: { inq: [locationThree] },
          locationFourId: { inq: [locationFour] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [locationOne] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [locationOne] },
          locationTwoId: { inq: [locationTwo] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [locationOne] },
          locationTwoId: { inq: [locationTwo] },
          locationThreeId: { inq: [locationThree] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }
}

