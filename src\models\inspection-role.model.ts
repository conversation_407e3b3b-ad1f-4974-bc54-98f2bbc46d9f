import {Entity, model, property} from '@loopback/repository';

@model()
export class InspectionRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  permission?: any;

  @property({
    type: 'date',
  })
  created?: string;


  constructor(data?: Partial<InspectionRole>) {
    super(data);
  }
}

export interface InspectionRoleRelations {
  // describe navigational properties here
}

export type InspectionRoleWithRelations = InspectionRole & InspectionRoleRelations;
